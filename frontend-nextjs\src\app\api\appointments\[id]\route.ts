import { NextRequest } from 'next/server';
import { withAuthentication, createSuccessResponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';
import { createPayloadClient } from '@/lib/payload-client';

export const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    const data = await payloadClient.getAppointment(params.id);
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error fetching appointment:', error);
    return createErrorResponse('Failed to fetch appointment');
  }
});

export const PATCH = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    const body = await request.json();
    
    const data = await payloadClient.updateAppointment(params.id, body);
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error updating appointment:', error);
    return createErrorResponse('Failed to update appointment');
  }
});

export const DELETE = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    await payloadClient.deleteAppointment(params.id);
    
    return createSuccessResponse(null, 204);
  } catch (error) {
    console.error('Error deleting appointment:', error);
    return createErrorResponse('Failed to delete appointment');
  }
});
