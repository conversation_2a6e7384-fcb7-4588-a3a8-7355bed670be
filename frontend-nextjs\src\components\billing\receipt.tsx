'use client';

import { forwardRef } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Payment, Bill } from '@/types/clinic';
import { billingUtils } from '@/lib/api/billing';

interface ReceiptProps {
  payment: Payment;
  bill?: Bill;
  clinicInfo?: {
    name: string;
    address: string;
    phone: string;
    email?: string;
    taxId?: string;
  };
}

// Default clinic information - this would typically come from settings
const defaultClinicInfo = {
  name: '美丽诊所',
  address: '北京市朝阳区美丽街123号',
  phone: '010-12345678',
  email: '<EMAIL>',
  taxId: '91110000000000000X',
};

export const Receipt = forwardRef<HTMLDivElement, ReceiptProps>(
  ({ payment, bill, clinicInfo = defaultClinicInfo }, ref) => {
    const patient = typeof payment.patient === 'object' ? payment.patient : null;
    const paymentBill = bill || (typeof payment.bill === 'object' ? payment.bill : null);

    return (
      <div ref={ref} className="max-w-md mx-auto bg-white">
        <Card className="shadow-none border-none">
          <CardHeader className="text-center pb-4">
            {/* Clinic Header */}
            <div className="space-y-1">
              <h1 className="text-xl font-bold">{clinicInfo.name}</h1>
              <p className="text-sm text-muted-foreground">{clinicInfo.address}</p>
              <p className="text-sm text-muted-foreground">
                电话: {clinicInfo.phone}
                {clinicInfo.email && ` | 邮箱: ${clinicInfo.email}`}
              </p>
              {clinicInfo.taxId && (
                <p className="text-xs text-muted-foreground">
                  税号: {clinicInfo.taxId}
                </p>
              )}
            </div>
            
            <Separator className="my-4" />
            
            {/* Receipt Title */}
            <div className="space-y-2">
              <h2 className="text-lg font-semibold">收款收据</h2>
              <div className="flex justify-between text-sm">
                <span>收据编号:</span>
                <span className="font-mono">{payment.receiptNumber || '待生成'}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>支付编号:</span>
                <span className="font-mono">{payment.paymentNumber}</span>
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Payment Information */}
            <div className="space-y-2">
              <h3 className="font-medium text-sm border-b pb-1">支付信息</h3>
              
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">患者姓名:</span>
                  <span>{patient?.fullName || '未知患者'}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-muted-foreground">支付日期:</span>
                  <span>{new Date(payment.paymentDate).toLocaleDateString('zh-CN')}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-muted-foreground">支付方式:</span>
                  <span>{billingUtils.getPaymentMethodName(payment.paymentMethod)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-muted-foreground">支付状态:</span>
                  <Badge variant={payment.paymentStatus === 'completed' ? 'default' : 'secondary'}>
                    {billingUtils.getPaymentStatusName(payment.paymentStatus)}
                  </Badge>
                </div>
              </div>

              {payment.transactionId && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">交易ID:</span>
                  <span className="font-mono text-xs">{payment.transactionId}</span>
                </div>
              )}
            </div>

            <Separator />

            {/* Bill Information */}
            {paymentBill && (
              <div className="space-y-2">
                <h3 className="font-medium text-sm border-b pb-1">账单信息</h3>
                
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">账单编号:</span>
                    <span className="font-mono">{paymentBill.billNumber}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">账单类型:</span>
                    <span>{billingUtils.getBillStatusName(paymentBill.billType)}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">服务描述:</span>
                    <span className="text-right max-w-32 truncate">
                      {paymentBill.description}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">账单总额:</span>
                    <span>{billingUtils.formatCurrency(paymentBill.totalAmount)}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">已支付:</span>
                    <span className="text-green-600">
                      {billingUtils.formatCurrency(paymentBill.paidAmount || 0)}
                    </span>
                  </div>
                  
                  {(paymentBill.remainingAmount || 0) > 0 && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">待支付:</span>
                      <span className="text-red-600">
                        {billingUtils.formatCurrency(paymentBill.remainingAmount || 0)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            <Separator />

            {/* Payment Amount */}
            <div className="space-y-2">
              <h3 className="font-medium text-sm border-b pb-1">本次支付</h3>
              
              <div className="bg-muted/30 rounded-lg p-3">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-medium">支付金额:</span>
                  <span className="text-xl font-bold text-green-600">
                    {billingUtils.formatCurrency(payment.amount)}
                  </span>
                </div>
              </div>
            </div>

            {/* Notes */}
            {payment.notes && (
              <>
                <Separator />
                <div className="space-y-2">
                  <h3 className="font-medium text-sm border-b pb-1">备注</h3>
                  <p className="text-sm text-muted-foreground">{payment.notes}</p>
                </div>
              </>
            )}

            <Separator />

            {/* Footer */}
            <div className="text-center space-y-2">
              <p className="text-xs text-muted-foreground">
                感谢您选择{clinicInfo.name}
              </p>
              <p className="text-xs text-muted-foreground">
                如有疑问，请联系我们: {clinicInfo.phone}
              </p>
              <p className="text-xs text-muted-foreground">
                打印时间: {new Date().toLocaleString('zh-CN')}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
);

Receipt.displayName = 'Receipt';
