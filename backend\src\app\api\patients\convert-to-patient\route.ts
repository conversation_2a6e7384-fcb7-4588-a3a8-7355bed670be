import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware';
import { Patient } from '../../../../payload-types';

/**
 * POST /api/patients/convert-to-patient - Convert consultation user to patient
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const { patientId, additionalData } = await request.json();

    if (!patientId) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      );
    }

    // First, get the current patient data
    const currentPatientResult = await makeAuthenticatedPayloadRequest(
      authContext,
      'patients',
      'findByID',
      {
        id: patientId,
      }
    );

    if (!currentPatientResult) {
      return NextResponse.json(
        { error: 'Patient not found' },
        { status: 404 }
      );
    }

    // Type cast to Patient
    const currentPatient = currentPatientResult as Patient;

    // Check if already a patient
    if (currentPatient.userType === 'patient') {
      return NextResponse.json(
        { error: 'User is already a patient' },
        { status: 400 }
      );
    }

    // Prepare update data
    const updateData = {
      userType: 'patient',
      status: 'converted',
      convertedAt: new Date().toISOString(),
      ...additionalData, // Allow additional patient-specific data
    };

    // Update patient in Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'patients',
      'update',
      {
        id: patientId,
        data: updateData,
      }
    );

    return NextResponse.json({
      message: 'Successfully converted consultation user to patient',
      patient: result,
    });
  } catch (error) {
    console.error('Error converting consultation user to patient:', error);
    return NextResponse.json(
      { error: 'Failed to convert consultation user to patient' },
      { status: 500 }
    );
  }
}
