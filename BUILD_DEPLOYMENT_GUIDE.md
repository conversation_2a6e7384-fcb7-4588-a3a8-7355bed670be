# Build & Deployment Guide - Medical Clinic System

This guide covers the build process, deployment procedures, and troubleshooting for the medical clinic management system.

## 🏗️ Build Process

### Frontend Build (Next.js)

#### Prerequisites
- Node.js 18+ 
- npm or pnpm package manager
- Environment variables configured

#### Build Commands
```bash
cd frontend-nextjs
npm run build
```

#### Build Output
- ✅ **17 pages generated** successfully
- ✅ **Static optimization** applied where possible
- ✅ **Bundle analysis** shows optimized chunks
- ✅ **Type checking** passes without errors

#### Build Verification
```bash
# Check build output
npm run build

# Expected output:
# ✓ Compiled successfully
# ✓ Linting and checking validity of types
# ✓ Creating an optimized production build
# ✓ Collecting page data
# ✓ Generating static pages (17/17)
# ✓ Finalizing page optimization
```

### Backend Build (Payload CMS)

#### Prerequisites
- Node.js 18+
- PostgreSQL database running
- Environment variables configured

#### Build Commands
```bash
cd backend
npm run build
```

#### Known Issues & Solutions

##### Next.js 15 Dynamic Routes (RESOLVED)
**Issue**: `params` must be awaited in dynamic API routes
**Solution**: Updated all API routes to use proper async pattern
```typescript
// Fixed pattern in all /api/[collection]/[id]/route.ts files
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  // ... rest of function
}
```

##### Favicon Requirements (RESOLVED)
**Issue**: Build failing due to missing favicon.ico
**Solution**: Added favicon.ico to backend/src/app/
```bash
# Favicon copied from frontend to backend
cp frontend-nextjs/src/app/favicon.ico backend/src/app/favicon.ico
```

## 🔧 Build Troubleshooting

### Common Build Errors

#### TypeScript Compilation Errors
**Symptoms**: Build fails with type errors
**Solutions**:
1. Check for proper type imports
2. Verify API request/response types match
3. Ensure form validation schemas are correct
4. Use type guards for dynamic data

#### React Hook Dependency Issues
**Symptoms**: Infinite re-render warnings during build
**Solutions**:
1. Avoid watching arrays from `form.watch()`
2. Use individual field watches instead
3. Memoize complex objects in dependency arrays
4. Check useEffect dependencies are stable

#### MSW Test Handler Issues
**Symptoms**: Test compilation errors during build
**Solutions**:
1. Type cast request bodies: `await request.json() as Record<string, any>`
2. Ensure all HTTP methods are properly mocked
3. Verify response structures match expected types

### Build Performance Optimization

#### Frontend Optimizations
- **Code Splitting**: Automatic with Next.js App Router
- **Image Optimization**: Next.js Image component used
- **Bundle Analysis**: Use `npm run build` to see chunk sizes
- **Tree Shaking**: Unused code automatically removed

#### Backend Optimizations
- **Database Connections**: Proper connection pooling
- **Static Assets**: Served efficiently through Next.js
- **API Response Caching**: Consider implementing for frequently accessed data

## 🚀 Deployment

### Environment Configuration

#### Frontend Environment Variables
```bash
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/dashboard"
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/dashboard"
```

#### Backend Environment Variables
```bash
# .env
DATABASE_URI=postgresql://username:password@host:port/database
PAYLOAD_SECRET=your-secret-key-here
PAYLOAD_CONFIG_PATH=src/payload.config.ts
```

### Production Deployment Steps

#### 1. Frontend Deployment (Vercel/Netlify)
```bash
# Build and deploy frontend
cd frontend-nextjs
npm run build
npm run start  # or deploy to platform
```

#### 2. Backend Deployment (Railway/Heroku/VPS)
```bash
# Build and deploy backend
cd backend
npm run build
npm run start  # or deploy to platform
```

#### 3. Database Setup
- Ensure PostgreSQL database is accessible
- Run any necessary migrations
- Verify connection strings are correct

### Health Checks

#### Frontend Health Check
```bash
curl http://localhost:3002/dashboard
# Should return 200 OK with dashboard content
```

#### Backend Health Check
```bash
curl http://localhost:3003/admin
# Should return Payload CMS admin interface
```

#### API Integration Check
```bash
curl http://localhost:3002/api/appointments
# Should return appointments data or authentication error
```

## 📊 Build Metrics

### Frontend Build Stats
- **Total Pages**: 17 static pages generated
- **Bundle Size**: Optimized for production
- **Build Time**: ~30-60 seconds typical
- **Type Safety**: 100% TypeScript coverage

### Backend Build Stats
- **Collections**: 5 main collections (Users, Patients, Treatments, Appointments, Media)
- **API Routes**: Full CRUD operations for all collections
- **Authentication**: Integrated with Clerk frontend auth
- **Build Time**: ~45-90 seconds typical

## 🔍 Monitoring & Maintenance

### Build Monitoring
- Monitor build times for performance regression
- Check bundle sizes for unexpected growth
- Verify all pages generate successfully
- Ensure type checking passes consistently

### Error Tracking
- Implement error boundaries in production
- Monitor API response times and errors
- Track authentication failures
- Log database connection issues

### Performance Monitoring
- Monitor Core Web Vitals
- Track API response times
- Monitor database query performance
- Check memory usage and optimization

## 📝 Build Checklist

### Pre-Build Checklist
- [ ] All environment variables configured
- [ ] Database connection verified
- [ ] Dependencies up to date
- [ ] Tests passing
- [ ] TypeScript compilation clean

### Post-Build Checklist
- [ ] All pages generated successfully
- [ ] No build warnings or errors
- [ ] Bundle sizes within acceptable limits
- [ ] Health checks passing
- [ ] Authentication flow working

### Deployment Checklist
- [ ] Production environment variables set
- [ ] Database migrations applied
- [ ] SSL certificates configured
- [ ] Domain names configured
- [ ] Monitoring and logging enabled

This guide ensures reliable builds and deployments for the medical clinic management system.
