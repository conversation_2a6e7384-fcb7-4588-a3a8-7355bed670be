'use client';

import { useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Receipt } from './receipt';
import { Payment, Bill } from '@/types/clinic';
import { IconPrinter, IconDownload, IconX } from '@tabler/icons-react';
import { toast } from 'sonner';
import { billingNotifications } from '@/lib/billing-notifications';

interface ReceiptDialogProps {
  payment: Payment | null;
  bill?: Bill;
  isOpen: boolean;
  onClose: () => void;
}

export function ReceiptDialog({ payment, bill, isOpen, onClose }: ReceiptDialogProps) {
  const receiptRef = useRef<HTMLDivElement>(null);

  const handlePrint = () => {
    if (!receiptRef.current) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      billingNotifications.receipt.printError();
      return;
    }

    const receiptContent = receiptRef.current.innerHTML;
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>收据 - ${payment?.receiptNumber || payment?.paymentNumber}</title>
          <meta charset="utf-8">
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              margin: 0;
              padding: 20px;
              background: white;
            }
            .max-w-md {
              max-width: 28rem;
            }
            .mx-auto {
              margin-left: auto;
              margin-right: auto;
            }
            .bg-white {
              background-color: white;
            }
            .shadow-none {
              box-shadow: none;
            }
            .border-none {
              border: none;
            }
            .text-center {
              text-align: center;
            }
            .text-xl {
              font-size: 1.25rem;
            }
            .text-lg {
              font-size: 1.125rem;
            }
            .text-sm {
              font-size: 0.875rem;
            }
            .text-xs {
              font-size: 0.75rem;
            }
            .font-bold {
              font-weight: 700;
            }
            .font-semibold {
              font-weight: 600;
            }
            .font-medium {
              font-weight: 500;
            }
            .font-mono {
              font-family: ui-monospace, SFMono-Regular, monospace;
            }
            .space-y-1 > * + * {
              margin-top: 0.25rem;
            }
            .space-y-2 > * + * {
              margin-top: 0.5rem;
            }
            .space-y-4 > * + * {
              margin-top: 1rem;
            }
            .pb-4 {
              padding-bottom: 1rem;
            }
            .pb-1 {
              padding-bottom: 0.25rem;
            }
            .p-3 {
              padding: 0.75rem;
            }
            .my-4 {
              margin-top: 1rem;
              margin-bottom: 1rem;
            }
            .border-b {
              border-bottom: 1px solid #e5e7eb;
            }
            .grid {
              display: grid;
            }
            .grid-cols-2 {
              grid-template-columns: repeat(2, minmax(0, 1fr));
            }
            .gap-2 {
              gap: 0.5rem;
            }
            .flex {
              display: flex;
            }
            .justify-between {
              justify-content: space-between;
            }
            .items-center {
              align-items: center;
            }
            .text-right {
              text-align: right;
            }
            .max-w-32 {
              max-width: 8rem;
            }
            .truncate {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .text-muted-foreground {
              color: #6b7280;
            }
            .text-green-600 {
              color: #059669;
            }
            .text-red-600 {
              color: #dc2626;
            }
            .bg-muted\\/30 {
              background-color: rgba(243, 244, 246, 0.3);
            }
            .rounded-lg {
              border-radius: 0.5rem;
            }
            hr {
              border: none;
              border-top: 1px solid #e5e7eb;
              margin: 1rem 0;
            }
            @media print {
              body {
                padding: 0;
              }
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          ${receiptContent}
        </body>
      </html>
    `);

    printWindow.document.close();
    printWindow.focus();
    
    // Wait for content to load then print
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);

    billingNotifications.receipt.printed(payment?.receiptNumber || payment?.paymentNumber || '');
  };

  const handleDownloadPDF = async () => {
    try {
      // This would typically use a library like jsPDF or html2pdf
      // For now, we'll show a placeholder message
      billingNotifications.system.featureNotImplemented('PDF下载');
      
      // Example implementation with html2pdf (would need to install the library):
      /*
      const html2pdf = (await import('html2pdf.js')).default;
      const element = receiptRef.current;
      
      const opt = {
        margin: 0.5,
        filename: `receipt-${payment?.receiptNumber || payment?.paymentNumber}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
      };
      
      html2pdf().set(opt).from(element).save();
      */
    } catch (error) {
      console.error('PDF generation failed:', error);
      billingNotifications.receipt.downloadError();
    }
  };

  if (!payment) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle>
              收据 - {payment.receiptNumber || payment.paymentNumber}
            </DialogTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handlePrint}>
                <IconPrinter className="h-4 w-4 mr-2" />
                打印
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownloadPDF}>
                <IconDownload className="h-4 w-4 mr-2" />
                下载PDF
              </Button>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <IconX className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>
        
        <div className="mt-4">
          <Receipt ref={receiptRef} payment={payment} bill={bill} />
        </div>
      </DialogContent>
    </Dialog>
  );
}
