'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { BillingList } from './billing-list';
import { AppointmentToBill } from './appointment-to-bill';
import { ReceiptManager } from './receipt-manager';
import { FinancialDashboard } from './financial-dashboard';
import { 
  IconReceipt, 
  IconCalendarEvent, 
  IconFileText,
  IconChartBar
} from '@tabler/icons-react';
import { useRole, PermissionGate } from '@/lib/role-context';
import { Bill } from '@/types/clinic';
import { toast } from 'sonner';

interface BillingTabsProps {
  defaultTab?: string;
  className?: string;
}

export function BillingTabs({ defaultTab = 'bills', className }: BillingTabsProps) {
  const { hasPermission } = useRole();
  const [activeTab, setActiveTab] = useState(defaultTab);

  const handleBillGenerated = (bill: Bill) => {
    toast.success(`账单生成成功！账单编号: ${bill.billNumber}`);
    // Switch to bills tab to show the newly created bill
    setActiveTab('bills');
  };

  return (
    <div className={className}>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
          <TabsTrigger value="bills" className="flex items-center gap-2">
            <IconReceipt className="h-4 w-4" />
            <span className="hidden sm:inline">账单管理</span>
            <span className="sm:hidden">账单</span>
          </TabsTrigger>
          
          <PermissionGate permission="canCreateBills">
            <TabsTrigger value="generate" className="flex items-center gap-2">
              <IconCalendarEvent className="h-4 w-4" />
              <span className="hidden sm:inline">预约生成</span>
              <span className="sm:hidden">生成</span>
            </TabsTrigger>
          </PermissionGate>
          
          <PermissionGate permission="canGenerateReceipts">
            <TabsTrigger value="receipts" className="flex items-center gap-2">
              <IconFileText className="h-4 w-4" />
              <span className="hidden sm:inline">收据管理</span>
              <span className="sm:hidden">收据</span>
            </TabsTrigger>
          </PermissionGate>
          
          <PermissionGate permission="canViewDetailedFinancials">
            <TabsTrigger value="reports" className="flex items-center gap-2">
              <IconChartBar className="h-4 w-4" />
              <span className="hidden sm:inline">财务报表</span>
              <span className="sm:hidden">报表</span>
            </TabsTrigger>
          </PermissionGate>
        </TabsList>

        {/* Bills Management Tab */}
        <TabsContent value="bills" className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold tracking-tight mb-2">账单管理</h2>
            <p className="text-muted-foreground mb-6">
              管理所有账单，处理支付和查看账单状态
            </p>
          </div>
          <BillingList />
        </TabsContent>

        {/* Generate from Appointments Tab */}
        <PermissionGate permission="canCreateBills">
          <TabsContent value="generate" className="space-y-6">
            <AppointmentToBill onBillGenerated={handleBillGenerated} />
          </TabsContent>
        </PermissionGate>

        {/* Receipt Management Tab */}
        <PermissionGate permission="canGenerateReceipts">
          <TabsContent value="receipts" className="space-y-6">
            <ReceiptManager />
          </TabsContent>
        </PermissionGate>

        {/* Financial Reports Tab */}
        <PermissionGate permission="canViewDetailedFinancials">
          <TabsContent value="reports" className="space-y-6">
            <FinancialDashboard />
          </TabsContent>
        </PermissionGate>
      </Tabs>
    </div>
  );
}
