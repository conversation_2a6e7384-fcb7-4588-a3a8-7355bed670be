'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { PaymentForm } from './payment-form';
import { Bill, Payment } from '@/types/clinic';

interface PaymentDialogProps {
  bill: Bill | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (payment: Payment) => void;
}

export function PaymentDialog({ bill, isOpen, onClose, onSuccess }: PaymentDialogProps) {
  const handleSuccess = (payment: Payment) => {
    if (onSuccess) {
      onSuccess(payment);
    }
    onClose();
  };

  if (!bill) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>处理支付 - {bill.billNumber}</DialogTitle>
        </DialogHeader>
        <PaymentForm
          bill={bill}
          onSuccess={handleSuccess}
          onCancel={onClose}
          isOpen={true}
        />
      </DialogContent>
    </Dialog>
  );
}
