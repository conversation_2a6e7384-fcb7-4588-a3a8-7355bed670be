// Comprehensive validation schemas for billing forms
// Provides robust client-side validation with detailed error messages in Chinese

import * as z from 'zod';

// Common validation patterns
const positiveNumber = z.number().min(0, '金额不能为负数');
const requiredString = z.string().min(1, '此字段为必填项');
const optionalString = z.string().optional();
const phoneRegex = /^1[3-9]\d{9}$/;
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Custom validation functions
const validateCurrency = (value: number) => {
  if (value < 0) return false;
  // Check for reasonable decimal places (max 2)
  const decimalPlaces = (value.toString().split('.')[1] || '').length;
  return decimalPlaces <= 2;
};

const validateDateNotInPast = (date: string) => {
  const inputDate = new Date(date);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return inputDate >= today;
};

const validateDateNotTooFarInFuture = (date: string) => {
  const inputDate = new Date(date);
  const maxDate = new Date();
  maxDate.setFullYear(maxDate.getFullYear() + 2); // Max 2 years in future
  return inputDate <= maxDate;
};

// Bill Item validation schema
export const billItemSchema = z.object({
  itemType: z.enum(['treatment', 'consultation', 'material', 'service'], {
    required_error: '请选择项目类型',
    invalid_type_error: '无效的项目类型',
  }),
  itemName: requiredString.max(100, '项目名称不能超过100个字符'),
  description: optionalString.max(500, '描述不能超过500个字符'),
  quantity: z.number()
    .min(0.01, '数量必须大于0')
    .max(9999, '数量不能超过9999')
    .refine((val) => {
      const decimalPlaces = (val.toString().split('.')[1] || '').length;
      return decimalPlaces <= 3;
    }, '数量最多支持3位小数'),
  unitPrice: z.number()
    .min(0, '单价不能为负数')
    .max(999999.99, '单价不能超过999,999.99')
    .refine(validateCurrency, '单价格式无效，最多支持2位小数'),
  discountRate: z.number()
    .min(0, '折扣率不能为负数')
    .max(100, '折扣率不能超过100%')
    .optional(),
}).refine((data) => {
  // Validate that discount rate makes sense
  if (data.discountRate && data.discountRate > 0 && data.unitPrice === 0) {
    return false;
  }
  return true;
}, {
  message: '单价为0时不能设置折扣',
  path: ['discountRate'],
});

// Bill form validation schema
export const billFormSchema = z.object({
  patient: requiredString.uuid('请选择有效的患者'),
  appointment: optionalString.uuid('请选择有效的预约').or(z.literal('')),
  treatment: optionalString.uuid('请选择有效的治疗项目').or(z.literal('')),
  billType: z.enum(['treatment', 'consultation', 'deposit', 'additional'], {
    required_error: '请选择账单类型',
    invalid_type_error: '无效的账单类型',
  }),
  description: requiredString
    .min(2, '账单描述至少需要2个字符')
    .max(200, '账单描述不能超过200个字符'),
  notes: optionalString.max(1000, '备注不能超过1000个字符'),
  dueDate: z.string()
    .min(1, '请选择到期日期')
    .refine((date) => {
      try {
        new Date(date);
        return true;
      } catch {
        return false;
      }
    }, '请输入有效的日期')
    .refine(validateDateNotInPast, '到期日期不能是过去的日期')
    .refine(validateDateNotTooFarInFuture, '到期日期不能超过2年'),
  discountAmount: z.number()
    .min(0, '折扣金额不能为负数')
    .max(999999.99, '折扣金额不能超过999,999.99')
    .refine(validateCurrency, '折扣金额格式无效')
    .optional(),
  taxAmount: z.number()
    .min(0, '税费金额不能为负数')
    .max(999999.99, '税费金额不能超过999,999.99')
    .refine(validateCurrency, '税费金额格式无效')
    .optional(),
  items: z.array(billItemSchema)
    .min(1, '至少需要一个账单项目')
    .max(50, '账单项目不能超过50个'),
}).refine((data) => {
  // Validate that bill has reasonable total
  const itemsTotal = data.items.reduce((sum, item) => {
    const itemTotal = item.quantity * item.unitPrice;
    const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);
    return sum + (itemTotal - itemDiscount);
  }, 0);
  
  const discountAmount = data.discountAmount || 0;
  const taxAmount = data.taxAmount || 0;
  const finalTotal = itemsTotal + taxAmount - discountAmount;
  
  return finalTotal >= 0;
}, {
  message: '账单总金额不能为负数',
  path: ['discountAmount'],
}).refine((data) => {
  // Validate discount doesn't exceed subtotal
  const itemsTotal = data.items.reduce((sum, item) => {
    const itemTotal = item.quantity * item.unitPrice;
    const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);
    return sum + (itemTotal - itemDiscount);
  }, 0);
  
  const discountAmount = data.discountAmount || 0;
  return discountAmount <= itemsTotal;
}, {
  message: '折扣金额不能超过项目小计',
  path: ['discountAmount'],
});

// Payment form validation schema
export const paymentFormSchema = z.object({
  amount: z.number()
    .min(0.01, '支付金额必须大于0')
    .max(999999.99, '支付金额不能超过999,999.99')
    .refine(validateCurrency, '支付金额格式无效，最多支持2位小数'),
  paymentMethod: z.enum(['cash', 'card', 'wechat', 'alipay', 'transfer', 'installment'], {
    required_error: '请选择支付方式',
    invalid_type_error: '无效的支付方式',
  }),
  transactionId: z.string()
    .max(100, '交易ID不能超过100个字符')
    .optional()
    .or(z.literal('')),
  notes: optionalString.max(500, '备注不能超过500个字符'),
}).refine((data) => {
  // Require transaction ID for certain payment methods
  const methodsRequiringTransactionId = ['card', 'wechat', 'alipay', 'transfer'];
  if (methodsRequiringTransactionId.includes(data.paymentMethod)) {
    return data.transactionId && data.transactionId.trim().length > 0;
  }
  return true;
}, {
  message: '此支付方式需要提供交易ID',
  path: ['transactionId'],
});

// Patient form validation schema (for billing context)
export const patientFormSchema = z.object({
  fullName: requiredString
    .min(2, '姓名至少需要2个字符')
    .max(50, '姓名不能超过50个字符')
    .regex(/^[\u4e00-\u9fa5a-zA-Z\s]+$/, '姓名只能包含中文、英文和空格'),
  phone: requiredString
    .regex(phoneRegex, '请输入有效的手机号码'),
  email: z.string()
    .email('请输入有效的邮箱地址')
    .max(100, '邮箱地址不能超过100个字符')
    .optional()
    .or(z.literal('')),
  medicalNotes: optionalString.max(2000, '医疗备注不能超过2000个字符'),
});

// Bill status update validation schema
export const billStatusUpdateSchema = z.object({
  status: z.enum(['draft', 'sent', 'confirmed', 'paid', 'cancelled'], {
    required_error: '请选择账单状态',
    invalid_type_error: '无效的账单状态',
  }),
  notes: optionalString.max(500, '状态更新备注不能超过500个字符'),
});

// Filter validation schema
export const billFilterSchema = z.object({
  search: optionalString.max(100, '搜索关键词不能超过100个字符'),
  status: z.enum(['draft', 'sent', 'confirmed', 'paid', 'cancelled']).optional(),
  billType: z.enum(['treatment', 'consultation', 'deposit', 'additional']).optional(),
  patientId: optionalString.uuid('请选择有效的患者').or(z.literal('')),
  dateFrom: z.string()
    .optional()
    .refine((date) => {
      if (!date) return true;
      try {
        new Date(date);
        return true;
      } catch {
        return false;
      }
    }, '请输入有效的开始日期'),
  dateTo: z.string()
    .optional()
    .refine((date) => {
      if (!date) return true;
      try {
        new Date(date);
        return true;
      } catch {
        return false;
      }
    }, '请输入有效的结束日期'),
  amountMin: z.number()
    .min(0, '最小金额不能为负数')
    .max(999999.99, '最小金额不能超过999,999.99')
    .optional(),
  amountMax: z.number()
    .min(0, '最大金额不能为负数')
    .max(999999.99, '最大金额不能超过999,999.99')
    .optional(),
}).refine((data) => {
  // Validate date range
  if (data.dateFrom && data.dateTo) {
    const fromDate = new Date(data.dateFrom);
    const toDate = new Date(data.dateTo);
    return fromDate <= toDate;
  }
  return true;
}, {
  message: '开始日期不能晚于结束日期',
  path: ['dateTo'],
}).refine((data) => {
  // Validate amount range
  if (data.amountMin !== undefined && data.amountMax !== undefined) {
    return data.amountMin <= data.amountMax;
  }
  return true;
}, {
  message: '最小金额不能大于最大金额',
  path: ['amountMax'],
});

// Export types for TypeScript
export type BillItemFormData = z.infer<typeof billItemSchema>;
export type BillFormData = z.infer<typeof billFormSchema>;
export type PaymentFormData = z.infer<typeof paymentFormSchema>;
export type PatientFormData = z.infer<typeof patientFormSchema>;
export type BillStatusUpdateData = z.infer<typeof billStatusUpdateSchema>;
export type BillFilterData = z.infer<typeof billFilterSchema>;

// Validation helper functions
export const validateBillItem = (data: unknown) => {
  return billItemSchema.safeParse(data);
};

export const validateBillForm = (data: unknown) => {
  return billFormSchema.safeParse(data);
};

export const validatePaymentForm = (data: unknown) => {
  return paymentFormSchema.safeParse(data);
};

export const validatePatientForm = (data: unknown) => {
  return patientFormSchema.safeParse(data);
};

export const validateBillStatusUpdate = (data: unknown) => {
  return billStatusUpdateSchema.safeParse(data);
};

export const validateBillFilter = (data: unknown) => {
  return billFilterSchema.safeParse(data);
};

// Custom validation error formatter
export const formatValidationErrors = (errors: z.ZodError) => {
  return errors.errors.map(error => ({
    field: error.path.join('.'),
    message: error.message,
    code: error.code,
  }));
};
