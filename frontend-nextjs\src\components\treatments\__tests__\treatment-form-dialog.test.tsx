import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, createMockTreatment } from '@/test/utils'
import { TreatmentFormDialog } from '../treatment-form-dialog'

describe('TreatmentFormDialog', () => {
  const mockOnOpenChange = vi.fn()
  const mockOnSuccess = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders create form when no treatment is provided', () => {
    render(
      <TreatmentFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    expect(screen.getByText('New Treatment')).toBeInTheDocument()
    expect(screen.getByText('Fill in the treatment details to create a new service.')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /create treatment/i })).toBeInTheDocument()
  })

  it('renders edit form when treatment is provided', () => {
    const mockTreatment = createMockTreatment()
    
    render(
      <TreatmentFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        treatment={mockTreatment}
        onSuccess={mockOnSuccess}
      />
    )

    expect(screen.getByText('Edit Treatment')).toBeInTheDocument()
    expect(screen.getByText('Update the treatment information below.')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /update treatment/i })).toBeInTheDocument()
  })

  it('pre-fills form fields when editing a treatment', () => {
    const mockTreatment = createMockTreatment({
      name: 'Botox Injection',
      description: 'Anti-aging treatment',
      defaultPrice: 300,
      defaultDurationInMinutes: 45
    })
    
    render(
      <TreatmentFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        treatment={mockTreatment}
        onSuccess={mockOnSuccess}
      />
    )

    // Check that form fields are pre-filled
    expect(screen.getByDisplayValue('Botox Injection')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Anti-aging treatment')).toBeInTheDocument()
    expect(screen.getByDisplayValue('300')).toBeInTheDocument()
    expect(screen.getByDisplayValue('45')).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()
    
    render(
      <TreatmentFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    // Try to submit without filling required fields
    const createButton = screen.getByRole('button', { name: /create treatment/i })
    await user.click(createButton)

    // Should show validation errors
    await waitFor(() => {
      expect(screen.getByText(/treatment name must be at least 2 characters/i)).toBeInTheDocument()
      expect(screen.getByText(/price must be positive/i)).toBeInTheDocument()
      expect(screen.getByText(/duration must be at least 1 minute/i)).toBeInTheDocument()
    })
  })

  it('validates price is positive', async () => {
    const user = userEvent.setup()
    
    render(
      <TreatmentFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    // Fill in name and negative price
    await user.type(screen.getByLabelText(/treatment name/i), 'Test Treatment')
    await user.clear(screen.getByLabelText(/default price/i))
    await user.type(screen.getByLabelText(/default price/i), '-10')
    await user.type(screen.getByLabelText(/duration/i), '30')
    
    const createButton = screen.getByRole('button', { name: /create treatment/i })
    await user.click(createButton)

    // Should show price validation error
    await waitFor(() => {
      expect(screen.getByText(/price must be positive/i)).toBeInTheDocument()
    })
  })

  it('validates duration is at least 1 minute', async () => {
    const user = userEvent.setup()
    
    render(
      <TreatmentFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    // Fill in name, price, and zero duration
    await user.type(screen.getByLabelText(/treatment name/i), 'Test Treatment')
    await user.type(screen.getByLabelText(/default price/i), '100')
    await user.clear(screen.getByLabelText(/duration/i))
    await user.type(screen.getByLabelText(/duration/i), '0')
    
    const createButton = screen.getByRole('button', { name: /create treatment/i })
    await user.click(createButton)

    // Should show duration validation error
    await waitFor(() => {
      expect(screen.getByText(/duration must be at least 1 minute/i)).toBeInTheDocument()
    })
  })

  it('closes dialog when cancel button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <TreatmentFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    const cancelButton = screen.getByRole('button', { name: /cancel/i })
    await user.click(cancelButton)

    expect(mockOnOpenChange).toHaveBeenCalledWith(false)
  })

  it('handles optional description field', async () => {
    const user = userEvent.setup()
    
    render(
      <TreatmentFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    // Fill in required fields only
    await user.type(screen.getByLabelText(/treatment name/i), 'Test Treatment')
    await user.type(screen.getByLabelText(/default price/i), '100')
    await user.type(screen.getByLabelText(/duration/i), '30')
    
    // Leave description empty
    const createButton = screen.getByRole('button', { name: /create treatment/i })
    await user.click(createButton)

    // Should not show validation errors for optional description
    await waitFor(() => {
      expect(screen.queryByText(/description/i)).toBeInTheDocument() // Label should exist
    })
  })

  it('displays loading state when submitting', async () => {
    render(
      <TreatmentFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    // The loading state would be tested when we have proper form submission
    // For now, verify the form structure
    expect(screen.getByRole('button', { name: /create treatment/i })).toBeInTheDocument()
  })

  it('renders all form fields', () => {
    render(
      <TreatmentFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    expect(screen.getByLabelText(/treatment name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/default price/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/duration/i)).toBeInTheDocument()
  })
})
