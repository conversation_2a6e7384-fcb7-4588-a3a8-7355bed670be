# 账单系统 API 参考文档

## 概述

本文档描述了医疗诊所账单管理系统的所有API端点，包括请求格式、响应格式和错误处理。

## 基础信息

- **基础URL**: `/api`
- **认证方式**: Clerk JWT <PERSON>ken
- **内容类型**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "docs": [...],           // 数据数组 (列表请求)
  "totalDocs": 100,        // 总记录数
  "page": 1,               // 当前页码
  "totalPages": 10,        // 总页数
  "hasNextPage": true,     // 是否有下一页
  "hasPrevPage": false     // 是否有上一页
}
```

### 错误响应
```json
{
  "error": "错误描述",
  "code": "ERROR_CODE",
  "details": {...}         // 详细错误信息 (可选)
}
```

## 账单管理 API

### 获取账单列表

**GET** `/api/bills`

#### 查询参数
| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| page | number | 否 | 页码 (默认: 1) |
| limit | number | 否 | 每页数量 (默认: 10, 最大: 100) |
| search | string | 否 | 搜索关键词 (账单号、患者姓名、描述) |
| status | string | 否 | 账单状态筛选 |
| billType | string | 否 | 账单类型筛选 |
| patient | string | 否 | 患者ID筛选 |
| dateFrom | string | 否 | 开始日期 (YYYY-MM-DD) |
| dateTo | string | 否 | 结束日期 (YYYY-MM-DD) |

#### 响应示例
```json
{
  "docs": [
    {
      "id": "bill_123",
      "billNumber": "BILL-2024-001",
      "patient": {
        "id": "patient_456",
        "fullName": "张小美",
        "phone": "13800138001"
      },
      "billType": "treatment",
      "status": "paid",
      "totalAmount": 1500,
      "paidAmount": 1500,
      "remainingAmount": 0,
      "issueDate": "2024-01-15T00:00:00.000Z",
      "dueDate": "2024-01-30T00:00:00.000Z",
      "description": "肉毒杆菌注射治疗",
      "items": [
        {
          "id": "item_789",
          "itemType": "treatment",
          "itemName": "肉毒杆菌注射",
          "quantity": 1,
          "unitPrice": 1500,
          "discountRate": 0
        }
      ]
    }
  ],
  "totalDocs": 1,
  "page": 1,
  "totalPages": 1
}
```

### 获取单个账单

**GET** `/api/bills/:id`

#### 路径参数
| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| id | string | 是 | 账单ID |

#### 响应示例
```json
{
  "id": "bill_123",
  "billNumber": "BILL-2024-001",
  "patient": {
    "id": "patient_456",
    "fullName": "张小美",
    "phone": "13800138001",
    "email": "<EMAIL>"
  },
  "appointment": {
    "id": "appointment_789",
    "appointmentDate": "2024-01-15T10:00:00.000Z"
  },
  "billType": "treatment",
  "status": "paid",
  "subtotal": 1500,
  "discountAmount": 0,
  "taxAmount": 0,
  "totalAmount": 1500,
  "paidAmount": 1500,
  "remainingAmount": 0,
  "issueDate": "2024-01-15T00:00:00.000Z",
  "dueDate": "2024-01-30T00:00:00.000Z",
  "paidDate": "2024-01-16T14:30:00.000Z",
  "description": "肉毒杆菌注射治疗",
  "notes": "患者对治疗效果满意",
  "items": [...],
  "payments": [...],
  "createdAt": "2024-01-15T09:00:00.000Z",
  "updatedAt": "2024-01-16T14:30:00.000Z",
  "createdBy": "user_admin"
}
```

### 创建账单

**POST** `/api/bills`

#### 请求体
```json
{
  "patient": "patient_456",
  "appointment": "appointment_789",
  "billType": "treatment",
  "description": "激光美容治疗",
  "notes": "首次治疗",
  "dueDate": "2024-02-15",
  "discountAmount": 100,
  "taxAmount": 0,
  "items": [
    {
      "itemType": "treatment",
      "itemName": "激光美容",
      "description": "面部激光美容治疗",
      "quantity": 1,
      "unitPrice": 2000,
      "discountRate": 5
    }
  ]
}
```

#### 响应
返回创建的账单对象 (同获取单个账单格式)

### 更新账单

**PATCH** `/api/bills/:id`

#### 请求体
```json
{
  "status": "confirmed",
  "notes": "患者已确认账单内容",
  "dueDate": "2024-02-20"
}
```

#### 响应
返回更新后的账单对象

### 删除账单

**DELETE** `/api/bills/:id`

#### 响应
```json
{
  "message": "账单删除成功",
  "deletedId": "bill_123"
}
```

### 从预约生成账单

**POST** `/api/bills/generate-from-appointment`

#### 请求体
```json
{
  "appointmentId": "appointment_789",
  "billType": "treatment"
}
```

#### 响应
返回生成的账单对象

## 支付管理 API

### 获取支付记录列表

**GET** `/api/payments`

#### 查询参数
| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| page | number | 否 | 页码 |
| limit | number | 否 | 每页数量 |
| bill | string | 否 | 账单ID筛选 |
| patient | string | 否 | 患者ID筛选 |
| paymentMethod | string | 否 | 支付方式筛选 |
| paymentStatus | string | 否 | 支付状态筛选 |
| dateFrom | string | 否 | 开始日期 |
| dateTo | string | 否 | 结束日期 |

### 处理支付

**POST** `/api/payments`

#### 请求体
```json
{
  "bill": "bill_123",
  "patient": "patient_456",
  "amount": 1500,
  "paymentMethod": "wechat",
  "transactionId": "wx_20240115_123456",
  "notes": "微信支付"
}
```

#### 响应示例
```json
{
  "id": "payment_789",
  "paymentNumber": "PAY-2024-001",
  "bill": "bill_123",
  "patient": "patient_456",
  "amount": 1500,
  "paymentMethod": "wechat",
  "paymentStatus": "completed",
  "transactionId": "wx_20240115_123456",
  "paymentDate": "2024-01-15T14:30:00.000Z",
  "receiptNumber": "RCP-2024-001",
  "notes": "微信支付",
  "createdAt": "2024-01-15T14:30:00.000Z"
}
```

### 处理退款

**POST** `/api/payments/:id/refund`

#### 请求体
```json
{
  "amount": 500,
  "reason": "部分退款",
  "notes": "患者要求退款"
}
```

## 财务报表 API

### 日收入报表

**GET** `/api/reports/daily-revenue`

#### 查询参数
| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| date | string | 是 | 日期 (YYYY-MM-DD) |

#### 响应示例
```json
{
  "date": "2024-01-15",
  "totalRevenue": 5000,
  "paymentCount": 3,
  "paymentMethods": {
    "cash": { "amount": 1000, "count": 1 },
    "wechat": { "amount": 2000, "count": 1 },
    "card": { "amount": 2000, "count": 1 }
  }
}
```

### 月收入报表

**GET** `/api/reports/monthly-revenue`

#### 查询参数
| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| year | number | 是 | 年份 |
| month | number | 是 | 月份 (1-12) |

### 应收账款报表

**GET** `/api/reports/outstanding-balances`

#### 响应示例
```json
{
  "totalOutstanding": 15000,
  "overdueAmount": 3000,
  "billsCount": 10,
  "overdueBillsCount": 2,
  "bills": [
    {
      "id": "bill_456",
      "billNumber": "BILL-2024-002",
      "patient": "李女士",
      "amount": 1500,
      "dueDate": "2024-01-10",
      "daysOverdue": 5
    }
  ]
}
```

## 错误代码

| 代码 | HTTP状态 | 描述 |
|------|----------|------|
| UNAUTHORIZED | 401 | 未授权访问 |
| FORBIDDEN | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| VALIDATION_ERROR | 400 | 请求数据验证失败 |
| BUSINESS_RULE_ERROR | 422 | 业务规则验证失败 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |

## 权限要求

| 端点 | 所需权限 |
|------|----------|
| GET /api/bills | canViewAllBills 或 canViewOwnBills |
| POST /api/bills | canCreateBills |
| PATCH /api/bills/:id | canEditBills |
| DELETE /api/bills/:id | canDeleteBills |
| POST /api/payments | canProcessPayments |
| GET /api/reports/* | canViewDetailedFinancials |

## 使用示例

### JavaScript/TypeScript
```typescript
// 获取账单列表
const response = await fetch('/api/bills?status=pending&limit=20', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
const bills = await response.json();

// 创建账单
const newBill = await fetch('/api/bills', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    patient: 'patient_123',
    billType: 'treatment',
    description: '激光治疗',
    dueDate: '2024-02-15',
    items: [...]
  })
});
```

### cURL
```bash
# 获取账单列表
curl -X GET "/api/bills?status=pending" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# 处理支付
curl -X POST "/api/payments" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "bill": "bill_123",
    "patient": "patient_456",
    "amount": 1500,
    "paymentMethod": "wechat"
  }'
```
