import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render } from '@/test/utils'
import PatientsPage from '../page'

// Mock the useAuth hook
vi.mock('@clerk/nextjs', () => ({
  useAuth: () => ({
    userId: 'test-user-id',
    isLoaded: true,
  }),
}))

describe('PatientsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders patients page with header', async () => {
    render(<PatientsPage />)
    
    expect(screen.getByText('Patient Records')).toBeInTheDocument()
    expect(screen.getByText(/manage patient information/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /new patient/i })).toBeInTheDocument()
  })

  it('displays loading state initially', () => {
    render(<PatientsPage />)
    
    expect(screen.getByText(/loading patients/i)).toBeInTheDocument()
  })

  it('displays search functionality', async () => {
    render(<PatientsPage />)
    
    await waitFor(() => {
      expect(screen.queryByText(/loading patients/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    const searchInput = screen.getByPlaceholderText(/search patients by name, phone, or email/i)
    expect(searchInput).toBeInTheDocument()
  })

  it('shows patient count badge', async () => {
    render(<PatientsPage />)
    
    await waitFor(() => {
      expect(screen.queryByText(/loading patients/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Should show patient count
    expect(screen.getByText(/patients/)).toBeInTheDocument()
  })

  it('opens patient form dialog when new patient button is clicked', async () => {
    const user = userEvent.setup()
    render(<PatientsPage />)
    
    await waitFor(() => {
      expect(screen.queryByText(/loading patients/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    const newButton = screen.getByRole('button', { name: /new patient/i })
    await user.click(newButton)

    // Should open the form dialog
    expect(screen.getByText('New Patient')).toBeInTheDocument()
  })

  it('filters patients based on search input', async () => {
    const user = userEvent.setup()
    render(<PatientsPage />)
    
    await waitFor(() => {
      expect(screen.queryByText(/loading patients/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    const searchInput = screen.getByPlaceholderText(/search patients by name, phone, or email/i)
    await user.type(searchInput, 'Alice')

    // Should filter patients (this would require MSW to return specific data)
    expect(searchInput).toHaveValue('Alice')
  })

  it('displays patient cards with correct information', async () => {
    render(<PatientsPage />)
    
    await waitFor(() => {
      expect(screen.queryByText(/loading patients/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Should display patient information (this would require MSW to return mock data)
    // For now, just verify the page structure exists
    expect(screen.getByText('Patient Records')).toBeInTheDocument()
  })

  it('shows action buttons on patient cards', async () => {
    render(<PatientsPage />)
    
    await waitFor(() => {
      expect(screen.queryByText(/loading patients/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Should show edit and delete buttons (this would require MSW to return mock data)
    // For now, just verify the page renders
    expect(screen.getByText('Patient Records')).toBeInTheDocument()
  })

  it('handles empty state correctly', async () => {
    render(<PatientsPage />)
    
    await waitFor(() => {
      expect(screen.queryByText(/loading patients/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Should handle empty state (this would require MSW to return empty data)
    // For now, just verify the page structure
    expect(screen.getByText('Patient Records')).toBeInTheDocument()
  })

  it('displays error state when API fails', async () => {
    render(<PatientsPage />)
    
    // Should handle error state (this would require MSW to return error)
    // For now, just verify the page renders
    await waitFor(() => {
      expect(screen.getByText('Patient Records')).toBeInTheDocument()
    }, { timeout: 3000 })
  })

  it('shows confirmation dialog when deleting patient', async () => {
    render(<PatientsPage />)
    
    await waitFor(() => {
      expect(screen.queryByText(/loading patients/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Should show confirmation dialog when delete is clicked
    // This would require MSW to return mock data and proper interaction
    expect(screen.getByText('Patient Records')).toBeInTheDocument()
  })

  it('prevents deletion of patients with appointments', async () => {
    render(<PatientsPage />)
    
    await waitFor(() => {
      expect(screen.queryByText(/loading patients/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Should prevent deletion if patient has appointments
    // This would require MSW to return mock data and proper interaction
    expect(screen.getByText('Patient Records')).toBeInTheDocument()
  })
})
