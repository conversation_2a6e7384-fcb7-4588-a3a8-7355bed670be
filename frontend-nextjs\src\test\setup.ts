import '@testing-library/jest-dom'
import React from 'react'
import { beforeAll, afterEach, afterAll, vi } from 'vitest'
import { cleanup } from '@testing-library/react'
import { server } from './mocks/server'

// Start server before all tests
beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))

// Clean up after each test case (e.g. clearing jsdom)
afterEach(() => {
  cleanup()
  server.resetHandlers()
})

// Close server after all tests
afterAll(() => server.close())

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock fetch globally
global.fetch = vi.fn()

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}))

// Mock Clerk authentication
vi.mock('@clerk/nextjs', () => ({
  useAuth: () => ({
    isLoaded: true,
    isSignedIn: true,
    userId: 'test-user-id',
    sessionId: 'test-session-id',
    getToken: vi.fn().mockResolvedValue('test-token'),
  }),
  useUser: () => ({
    isLoaded: true,
    isSignedIn: true,
    user: {
      id: 'test-user-id',
      emailAddresses: [{ emailAddress: '<EMAIL>' }],
      firstName: 'Test',
      lastName: 'User',
    },
  }),
  SignInButton: ({ children }: { children: React.ReactNode }) => children,
  SignOutButton: ({ children }: { children: React.ReactNode }) => children,
  UserButton: () => 'User Button',
}))

vi.mock('@clerk/nextjs/server', () => ({
  auth: () => Promise.resolve({
    userId: 'test-user-id',
    sessionId: 'test-session-id',
    getToken: vi.fn().mockResolvedValue('test-token'),
  }),
}))

// Mock role context
vi.mock('@/lib/role-context', async () => {
  const actual = await vi.importActual('@/lib/role-context')
  return {
    ...actual,
    useRole: () => ({
      user: {
        clerkId: 'test-clerk-id',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'admin',
        payloadUserId: 'test-payload-user-1',
      },
      loading: false,
      error: null,
      permissions: {
        canViewDashboard: true,
        canViewAppointments: true,
        canCreateAppointments: true,
        canEditAppointments: true,
        canDeleteAppointments: true,
        canViewPatients: true,
        canCreatePatients: true,
        canEditPatients: true,
        canDeletePatients: true,
        canViewTreatments: true,
        canCreateTreatments: true,
        canEditTreatments: true,
        canDeleteTreatments: true,
        canViewMedicalNotes: true,
        canEditMedicalNotes: true,
        canViewAllAppointments: true,
        canManageUsers: true,
        canViewReports: true,
      },
      hasPermission: () => true,
      hasRole: () => true,
      refreshUser: vi.fn(),
    }),
    PermissionGate: ({ children }: { children: React.ReactNode }) => children,
  }
})

// Mock nuqs for URL state management
vi.mock('nuqs', () => {
  const createParser = (defaultValue: any) => ({
    withDefault: vi.fn(() => createParser(defaultValue)),
    withOptions: vi.fn(() => createParser(defaultValue)),
    parse: vi.fn(() => defaultValue)
  });

  return {
    useQueryState: vi.fn(() => [1, vi.fn()]),
    useQueryStates: vi.fn(() => [{ page: 1, perPage: 10 }, vi.fn()]),
    parseAsInteger: createParser(1),
    parseAsString: createParser(''),
    parseAsArrayOf: vi.fn(() => createParser([])),
  };
})
