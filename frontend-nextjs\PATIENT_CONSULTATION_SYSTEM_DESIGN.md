# 患者与咨询用户管理系统设计

## 系统概述

为了更好地管理医疗诊所的不同类型用户，我们设计了一个分层的用户管理系统，区分患者和咨询用户，同时提供灵活的转换机制。

## 用户分类体系

### 1. 用户状态分类

#### A. 咨询用户 (Consultation Users)
- **定义**: 对诊所服务感兴趣，但尚未成为正式患者的用户
- **特征**:
  - 仅提供基本联系信息
  - 主要用于咨询预约
  - 无完整病历信息
  - 可随时转换为正式患者

#### B. 正式患者 (Registered Patients)
- **定义**: 已接受治疗或完成详细信息登记的用户
- **特征**:
  - 完整的个人信息
  - 详细的病历记录
  - 治疗历史
  - 支付记录

### 2. 预约类型分类

#### A. 咨询预约 (Consultation Appointments)
- **目的**: 了解服务、价格咨询、初步评估
- **特点**:
  - 通常较短时间（15-30分钟）
  - 价格较低或免费
  - 重点在信息提供
  - 可能转化为治疗预约

#### B. 治疗预约 (Treatment Appointments)
- **目的**: 实际医疗服务提供
- **特点**:
  - 时间较长
  - 需要完整患者信息
  - 涉及医疗记录
  - 需要支付管理

## 数据库设计

### 用户表结构扩展

```typescript
interface User {
  id: string;
  userType: 'consultation' | 'patient'; // 用户类型
  status: 'active' | 'inactive' | 'converted'; // 用户状态
  
  // 基本信息（所有用户必填）
  fullName: string;
  phone: string;
  email?: string;
  
  // 患者专用信息（可选）
  medicalNotes?: string;
  emergencyContact?: string;
  allergies?: string[];
  medicalHistory?: string;
  
  // 元数据
  createdAt: Date;
  updatedAt: Date;
  convertedAt?: Date; // 从咨询用户转为患者的时间
  lastVisit?: Date;
  
  // 来源追踪
  source: 'walk-in' | 'referral' | 'online' | 'phone';
  referredBy?: string;
}
```

### 预约表结构扩展

```typescript
interface Appointment {
  id: string;
  appointmentType: 'consultation' | 'treatment'; // 预约类型
  
  // 关联信息
  userId: string; // 关联用户（咨询用户或患者）
  treatmentId?: string; // 治疗预约必填
  
  // 预约详情
  appointmentDate: Date;
  duration: number;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
  
  // 咨询预约专用
  consultationType?: 'initial' | 'follow-up' | 'price-inquiry';
  interestedTreatments?: string[]; // 感兴趣的治疗项目
  
  // 治疗预约专用
  price?: number;
  paymentStatus?: 'pending' | 'partial' | 'paid' | 'overdue';
  
  // 记录
  notes?: string;
  outcome?: 'converted' | 'scheduled-treatment' | 'no-interest' | 'follow-up-needed';
}
```

## 工作流程设计

### 1. 咨询用户流程

```mermaid
graph TD
    A[新咨询用户] --> B[填写基本信息]
    B --> C[预约咨询]
    C --> D[咨询会面]
    D --> E{咨询结果}
    E -->|感兴趣| F[转为正式患者]
    E -->|需要考虑| G[安排后续咨询]
    E -->|不感兴趣| H[标记为无兴趣]
    F --> I[预约治疗]
    G --> D
```

### 2. 患者管理流程

```mermaid
graph TD
    A[正式患者] --> B[完善病历信息]
    B --> C[预约治疗]
    C --> D[治疗服务]
    D --> E[支付管理]
    E --> F[后续预约]
    F --> D
```

## 界面设计建议

### 1. 用户管理页面

#### 统一用户列表
- **筛选选项**:
  - 用户类型：全部 / 咨询用户 / 正式患者
  - 状态：活跃 / 非活跃 / 已转换
  - 最后访问时间
  - 来源渠道

#### 用户卡片显示
- **咨询用户卡片**:
  - 绿色边框标识
  - 显示"咨询用户"标签
  - 显示咨询次数
  - "转为患者"快捷按钮

- **正式患者卡片**:
  - 蓝色边框标识
  - 显示"正式患者"标签
  - 显示治疗次数
  - 显示最后就诊时间

### 2. 预约管理页面

#### 预约类型区分
- **咨询预约**:
  - 橙色标识
  - 显示咨询类型
  - 转换追踪

- **治疗预约**:
  - 蓝色标识
  - 显示治疗项目
  - 支付状态

### 3. 新建用户/预约流程

#### 智能表单
- 根据预约类型动态显示字段
- 咨询预约：简化表单
- 治疗预约：完整表单 + 患者信息验证

## 业务规则

### 1. 用户转换规则
- 咨询用户可随时转为正式患者
- 转换时需补充完整信息
- 保留原有咨询记录
- 自动更新用户状态

### 2. 预约限制规则
- 咨询用户只能预约咨询类型
- 治疗预约需要正式患者身份
- 系统自动提示转换需求

### 3. 数据保护规则
- 咨询用户信息保护期：6个月
- 患者信息永久保存
- 敏感信息访问权限控制

## 实施计划

### 阶段一：数据结构升级
1. 扩展用户表结构
2. 添加用户类型字段
3. 创建数据迁移脚本
4. 更新API接口

### 阶段二：界面改造
1. 更新用户管理页面
2. 添加用户类型筛选
3. 实现转换功能
4. 优化预约流程

### 阶段三：业务逻辑完善
1. 实现自动转换提示
2. 添加转换追踪
3. 完善权限控制
4. 优化用户体验

## 预期效果

### 1. 管理效率提升
- 清晰的用户分类
- 简化的咨询流程
- 高效的转换机制

### 2. 业务增长支持
- 更好的潜在客户管理
- 提高转换率追踪
- 优化营销策略

### 3. 用户体验改善
- 降低初次接触门槛
- 渐进式信息收集
- 个性化服务提供

## 技术实现要点

### 1. 状态管理
- 使用状态机管理用户生命周期
- 实现状态转换的原子性操作
- 添加状态变更日志

### 2. 权限控制
- 基于用户类型的权限设计
- 敏感信息访问控制
- 操作审计日志

### 3. 数据一致性
- 用户转换时的数据完整性检查
- 关联数据的自动更新
- 事务性操作保证

这个设计方案提供了灵活而完整的用户管理体系，既满足了咨询用户的轻量化需求，又保证了正式患者的完整管理，同时提供了平滑的转换机制。
