'use client'

import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { PermissionGate } from '@/lib/role-context'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { patientsApi } from '@/lib/api'
import { Patient } from '@/types/clinic'
import { toast } from 'sonner'
import { formatApiError, formatSuccessMessage, formatLoadingMessage } from '@/lib/error-utils'
import { t } from '@/lib/translations'

const patientSchema = z.object({
  fullName: z.string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name cannot exceed 100 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Full name can only contain letters, spaces, hyphens, and apostrophes'),
  phone: z.string()
    .min(10, 'Phone number must be at least 10 digits')
    .max(15, 'Phone number cannot exceed 15 digits')
    .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number (e.g., +********** or **********)'),
  email: z.string()
    .email('Please enter a valid email address (e.g., <EMAIL>)')
    .optional()
    .or(z.literal('')),
  medicalNotes: z.string()
    .max(2000, 'Medical notes cannot exceed 2000 characters')
    .optional(),
})

type PatientFormData = z.infer<typeof patientSchema>

interface PatientFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  patient?: Patient
  onSuccess?: () => void
}

export function PatientFormDialog({
  open,
  onOpenChange,
  patient,
  onSuccess,
}: PatientFormDialogProps) {
  const [loading, setLoading] = useState(false)

  const isEditing = !!patient

  const form = useForm<PatientFormData>({
    resolver: zodResolver(patientSchema),
    defaultValues: {
      fullName: patient?.fullName || '',
      phone: patient?.phone || '',
      email: patient?.email || '',
      medicalNotes: patient?.medicalNotes || '',
    },
  })

  // Reset form when patient changes or dialog opens
  React.useEffect(() => {
    if (open) {
      form.reset({
        fullName: patient?.fullName || '',
        phone: patient?.phone || '',
        email: patient?.email || '',
        medicalNotes: patient?.medicalNotes || '',
      })
    }
  }, [open, patient, form])

  const onSubmit = async (data: PatientFormData) => {
    setLoading(true)

    // Show loading toast
    const loadingToast = toast.loading(
      formatLoadingMessage(isEditing ? 'update' : 'create', 'patient')
    );

    try {
      const patientData = {
        fullName: data.fullName,
        phone: data.phone,
        email: data.email || undefined,
        medicalNotes: data.medicalNotes || undefined,
      }

      if (isEditing) {
        await patientsApi.update(patient.id, patientData)
        toast.success(formatSuccessMessage('update', 'patient'), { id: loadingToast })
      } else {
        await patientsApi.create(patientData)
        toast.success(formatSuccessMessage('create', 'patient'), { id: loadingToast })
      }

      onSuccess?.()
      onOpenChange(false)
      form.reset()
    } catch (error) {
      console.error('Failed to save patient:', error)
      const errorMessage = formatApiError(error);
      toast.error(errorMessage, { id: loadingToast })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? t('patients.editPatient') : t('patients.newPatient')}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? '更新下方的患者信息。'
              : '填写患者详细信息以创建新记录。'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Full Name */}
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('patients.form.fullName')} *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t('patients.form.fullNamePlaceholder')}
                      {...field}
                    />
                  </FormControl>
                  <p className="text-xs text-muted-foreground">
                    请输入患者的完整法定姓名，与身份证件上的姓名一致
                  </p>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Phone */}
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('patients.form.phone')} *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t('patients.form.phonePlaceholder')}
                      type="tel"
                      {...field}
                    />
                  </FormControl>
                  <p className="text-xs text-muted-foreground">
                    用于预约提醒和更新的主要联系电话
                  </p>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Email */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('patients.form.email')} (可选)</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t('patients.form.emailPlaceholder')}
                      type="email"
                      {...field}
                    />
                  </FormControl>
                  <p className="text-xs text-muted-foreground">
                    用于预约确认和电子收据
                  </p>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Medical Notes - Only visible to Admin and Doctor */}
            <PermissionGate permission="canEditMedicalNotes">
              <FormField
                control={form.control}
                name="medicalNotes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('patients.form.medicalNotes')}</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t('patients.form.medicalNotesPlaceholder')}
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                    <p className="text-xs text-muted-foreground">
                      机密医疗信息 - 仅限医务人员查看
                    </p>
                  </FormItem>
                )}
              />
            </PermissionGate>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                {t('common.actions.cancel')}
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? '保存中...' : isEditing ? '更新患者' : '创建患者'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
