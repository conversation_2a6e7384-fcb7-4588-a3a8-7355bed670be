import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../lib/payload-auth-middleware';

/**
 * GET /api/appointments - Get all appointments
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Extract query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const page = parseInt(url.searchParams.get('page') || '1');
    const depth = parseInt(url.searchParams.get('depth') || '2');

    // Fetch appointments from Payload CMS with relationships
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'appointments',
      'find',
      {
        limit,
        page,
        depth, // Include patient, treatment, and practitioner relationships
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching appointments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch appointments' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/appointments - Create a new appointment
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const appointmentData = await request.json();

    // Create appointment in Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'appointments',
      'create',
      {
        data: appointmentData,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error creating appointment:', error);
    return NextResponse.json(
      { error: 'Failed to create appointment' },
      { status: 500 }
    );
  }
}
