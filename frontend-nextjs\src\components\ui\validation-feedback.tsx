'use client';

import { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  IconAlertTriangle, 
  IconAlertCircle, 
  IconCheck, 
  IconX,
  IconEye,
  IconEyeOff
} from '@tabler/icons-react';
import { ValidationResult, ValidationError, ValidationWarning } from '@/lib/validation/validation-utils';
import { cn } from '@/lib/utils';

interface ValidationFeedbackProps {
  result?: ValidationResult;
  className?: string;
  showWarnings?: boolean;
  compact?: boolean;
}

export function ValidationFeedback({ 
  result, 
  className, 
  showWarnings = true,
  compact = false 
}: ValidationFeedbackProps) {
  const [showDetails, setShowDetails] = useState(false);

  if (!result || (result.errors.length === 0 && result.warnings.length === 0)) {
    return null;
  }

  const errorCount = result.errors.length;
  const warningCount = result.warnings.length;
  const hasErrors = errorCount > 0;
  const hasWarnings = warningCount > 0;

  if (compact) {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        {hasErrors && (
          <Badge variant="destructive" className="flex items-center gap-1">
            <IconAlertCircle className="h-3 w-3" />
            {errorCount} 错误
          </Badge>
        )}
        {hasWarnings && showWarnings && (
          <Badge variant="secondary" className="flex items-center gap-1">
            <IconAlertTriangle className="h-3 w-3" />
            {warningCount} 警告
          </Badge>
        )}
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      {/* Summary */}
      {(hasErrors || hasWarnings) && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {hasErrors && (
              <Badge variant="destructive" className="flex items-center gap-1">
                <IconAlertCircle className="h-3 w-3" />
                {errorCount} 个错误
              </Badge>
            )}
            {hasWarnings && showWarnings && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <IconAlertTriangle className="h-3 w-3" />
                {warningCount} 个警告
              </Badge>
            )}
          </div>
          
          {(errorCount + warningCount) > 3 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className="h-6 px-2"
            >
              {showDetails ? (
                <>
                  <IconEyeOff className="h-3 w-3 mr-1" />
                  隐藏详情
                </>
              ) : (
                <>
                  <IconEye className="h-3 w-3 mr-1" />
                  显示详情
                </>
              )}
            </Button>
          )}
        </div>
      )}

      {/* Error Details */}
      {hasErrors && (
        <div className="space-y-1">
          {result.errors
            .slice(0, showDetails ? undefined : 3)
            .map((error, index) => (
            <ValidationErrorItem key={index} error={error} />
          ))}
          
          {!showDetails && errorCount > 3 && (
            <div className="text-xs text-muted-foreground pl-4">
              还有 {errorCount - 3} 个错误...
            </div>
          )}
        </div>
      )}

      {/* Warning Details */}
      {hasWarnings && showWarnings && (
        <div className="space-y-1">
          {result.warnings
            .slice(0, showDetails ? undefined : 2)
            .map((warning, index) => (
            <ValidationWarningItem key={index} warning={warning} />
          ))}
          
          {!showDetails && warningCount > 2 && (
            <div className="text-xs text-muted-foreground pl-4">
              还有 {warningCount - 2} 个警告...
            </div>
          )}
        </div>
      )}
    </div>
  );
}

interface ValidationErrorItemProps {
  error: ValidationError;
}

function ValidationErrorItem({ error }: ValidationErrorItemProps) {
  const getFieldDisplayName = (field: string): string => {
    const fieldNames: Record<string, string> = {
      'patient': '患者',
      'amount': '金额',
      'paymentMethod': '支付方式',
      'transactionId': '交易ID',
      'billType': '账单类型',
      'description': '描述',
      'dueDate': '到期日期',
      'items': '账单项目',
      'itemName': '项目名称',
      'quantity': '数量',
      'unitPrice': '单价',
      'discountRate': '折扣率',
      'discountAmount': '折扣金额',
      'taxAmount': '税费金额',
      'notes': '备注',
    };
    
    return fieldNames[field] || field;
  };

  return (
    <Alert variant="destructive" className="py-2">
      <IconAlertCircle className="h-4 w-4" />
      <AlertDescription className="text-sm">
        <span className="font-medium">{getFieldDisplayName(error.field)}: </span>
        {error.message}
      </AlertDescription>
    </Alert>
  );
}

interface ValidationWarningItemProps {
  warning: ValidationWarning;
}

function ValidationWarningItem({ warning }: ValidationWarningItemProps) {
  const getFieldDisplayName = (field: string): string => {
    const fieldNames: Record<string, string> = {
      'amount': '金额',
      'dueDate': '到期日期',
      'discountAmount': '折扣金额',
      'unitPrice': '单价',
      'items': '账单项目',
      'form': '表单',
    };
    
    return fieldNames[warning.field] || warning.field;
  };

  return (
    <Alert className="py-2 border-yellow-200 bg-yellow-50">
      <IconAlertTriangle className="h-4 w-4 text-yellow-600" />
      <AlertDescription className="text-sm">
        <div>
          <span className="font-medium text-yellow-800">
            {getFieldDisplayName(warning.field)}: 
          </span>
          <span className="text-yellow-700 ml-1">{warning.message}</span>
        </div>
        {warning.suggestion && (
          <div className="text-xs text-yellow-600 mt-1">
            建议: {warning.suggestion}
          </div>
        )}
      </AlertDescription>
    </Alert>
  );
}

// Field-level validation feedback component
interface FieldValidationFeedbackProps {
  error?: string;
  warning?: string;
  success?: boolean;
  className?: string;
}

export function FieldValidationFeedback({ 
  error, 
  warning, 
  success, 
  className 
}: FieldValidationFeedbackProps) {
  if (!error && !warning && !success) {
    return null;
  }

  return (
    <div className={cn('flex items-center gap-1 mt-1', className)}>
      {error && (
        <div className="flex items-center gap-1 text-red-600">
          <IconAlertCircle className="h-3 w-3" />
          <span className="text-xs">{error}</span>
        </div>
      )}
      
      {warning && !error && (
        <div className="flex items-center gap-1 text-yellow-600">
          <IconAlertTriangle className="h-3 w-3" />
          <span className="text-xs">{warning}</span>
        </div>
      )}
      
      {success && !error && !warning && (
        <div className="flex items-center gap-1 text-green-600">
          <IconCheck className="h-3 w-3" />
          <span className="text-xs">验证通过</span>
        </div>
      )}
    </div>
  );
}

// Real-time validation status indicator
interface ValidationStatusProps {
  isValid: boolean;
  isValidating?: boolean;
  errorCount?: number;
  warningCount?: number;
  className?: string;
}

export function ValidationStatus({ 
  isValid, 
  isValidating = false, 
  errorCount = 0, 
  warningCount = 0,
  className 
}: ValidationStatusProps) {
  if (isValidating) {
    return (
      <div className={cn('flex items-center gap-2 text-muted-foreground', className)}>
        <div className="animate-spin rounded-full h-3 w-3 border-b border-current"></div>
        <span className="text-xs">验证中...</span>
      </div>
    );
  }

  if (isValid && errorCount === 0) {
    return (
      <div className={cn('flex items-center gap-2 text-green-600', className)}>
        <IconCheck className="h-3 w-3" />
        <span className="text-xs">
          表单验证通过
          {warningCount > 0 && ` (${warningCount} 个警告)`}
        </span>
      </div>
    );
  }

  return (
    <div className={cn('flex items-center gap-2 text-red-600', className)}>
      <IconX className="h-3 w-3" />
      <span className="text-xs">
        {errorCount} 个错误
        {warningCount > 0 && `, ${warningCount} 个警告`}
      </span>
    </div>
  );
}
