import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../lib/payload-auth-middleware';

/**
 * GET /api/bills - Get all bills
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Extract query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const page = parseInt(url.searchParams.get('page') || '1');
    const depth = parseInt(url.searchParams.get('depth') || '2');
    const status = url.searchParams.get('status');
    const billType = url.searchParams.get('billType');
    const patientId = url.searchParams.get('patientId');

    // Build where clause for filtering
    const where: Record<string, unknown> = {};
    if (status) {
      where.status = { equals: status };
    }
    if (billType) {
      where.billType = { equals: billType };
    }
    if (patientId) {
      where.patient = { equals: patientId };
    }

    // Fetch bills from Payload CMS with relationships
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'bills',
      'find',
      {
        limit,
        page,
        depth, // Include patient, appointment, treatment, and createdBy relationships
        where: Object.keys(where).length > 0 ? where : undefined,
        sort: '-createdAt', // Sort by newest first
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching bills:', error);
    return NextResponse.json(
      { error: 'Failed to fetch bills' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/bills - Create a new bill
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const billData = await request.json();

    // Add the current user as the creator
    billData.createdBy = authContext.user.id;

    // Validate required fields
    if (!billData.patient) {
      return NextResponse.json(
        { error: 'Patient is required' },
        { status: 400 }
      );
    }

    if (!billData.subtotal || billData.subtotal < 0) {
      return NextResponse.json(
        { error: 'Valid subtotal is required' },
        { status: 400 }
      );
    }

    if (!billData.totalAmount || billData.totalAmount < 0) {
      return NextResponse.json(
        { error: 'Valid total amount is required' },
        { status: 400 }
      );
    }

    if (!billData.description) {
      return NextResponse.json(
        { error: 'Description is required' },
        { status: 400 }
      );
    }

    // Set default due date if not provided (30 days from now)
    if (!billData.dueDate) {
      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + 30);
      billData.dueDate = dueDate.toISOString();
    }

    // Create bill in Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'bills',
      'create',
      {
        data: billData,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error creating bill:', error);
    return NextResponse.json(
      { error: 'Failed to create bill' },
      { status: 500 }
    );
  }
}
