import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware';

/**
 * GET /api/bill-items/[id] - Get a specific bill item
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Fetch bill item from Payload CMS with relationships
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'bill-items',
      'findByID',
      {
        id,
        depth: 2, // Include bill relationship
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching bill item:', error);
    return NextResponse.json(
      { error: 'Failed to fetch bill item' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/bill-items/[id] - Update a specific bill item
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const updateData = await request.json();

    // Validate fields if provided
    if (updateData.quantity !== undefined && updateData.quantity <= 0) {
      return NextResponse.json(
        { error: 'Quantity must be greater than 0' },
        { status: 400 }
      );
    }

    if (updateData.unitPrice !== undefined && updateData.unitPrice < 0) {
      return NextResponse.json(
        { error: 'Unit price cannot be negative' },
        { status: 400 }
      );
    }

    if (updateData.discountRate !== undefined && (updateData.discountRate < 0 || updateData.discountRate > 100)) {
      return NextResponse.json(
        { error: 'Discount rate must be between 0 and 100' },
        { status: 400 }
      );
    }

    // Update bill item in Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'bill-items',
      'update',
      {
        id,
        data: updateData,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating bill item:', error);
    return NextResponse.json(
      { error: 'Failed to update bill item' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/bill-items/[id] - Delete a specific bill item
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Delete bill item from Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'bill-items',
      'delete',
      {
        id,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error deleting bill item:', error);
    return NextResponse.json(
      { error: 'Failed to delete bill item' },
      { status: 500 }
    );
  }
}
