import { NextRequest } from 'next/server';
import { withAuthentication, createSuccessResponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';
import { createPayloadClient } from '@/lib/payload-client';

export const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    const data = await payloadClient.getPatient(params.id);
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error fetching patient:', error);
    return createErrorResponse('Failed to fetch patient');
  }
});

export const PATCH = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    const body = await request.json();
    
    const data = await payloadClient.updatePatient(params.id, body);
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error updating patient:', error);
    return createErrorResponse('Failed to update patient');
  }
});

export const DELETE = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    await payloadClient.deletePatient(params.id);
    
    return createSuccessResponse(null, 204);
  } catch (error) {
    console.error('Error deleting patient:', error);
    return createErrorResponse('Failed to delete patient');
  }
});
