// Simple test script to verify API endpoints
const BASE_URL = 'http://localhost:8002';

async function testEndpoint(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const result = await response.json();
    
    console.log(`\n${method} ${endpoint}`);
    console.log(`Status: ${response.status}`);
    console.log('Response:', JSON.stringify(result, null, 2));
    
    return { status: response.status, data: result };
  } catch (error) {
    console.error(`Error testing ${endpoint}:`, error.message);
    return { error: error.message };
  }
}

async function runTests() {
  console.log('Testing API endpoints...\n');

  // Test basic connectivity
  await testEndpoint('/api/test');

  // Test Bills endpoints (without auth - should fail)
  await testEndpoint('/api/bills');
  await testEndpoint('/api/bill-items');
  await testEndpoint('/api/payments');
  await testEndpoint('/api/deposits');

  // Test Patients endpoints (without auth - should fail)
  await testEndpoint('/api/patients');

  // Test Appointments endpoints (without auth - should fail)
  await testEndpoint('/api/appointments');

  console.log('\nAPI endpoint tests completed.');
  console.log('Note: Authentication failures are expected without proper auth headers.');
}

runTests();
