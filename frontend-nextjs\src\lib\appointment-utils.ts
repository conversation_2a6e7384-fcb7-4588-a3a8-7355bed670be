// Appointment scheduling utilities and conflict detection

import { Appointment } from '@/types/clinic';

export interface AppointmentConflict {
  hasConflict: boolean;
  conflictingAppointment?: Appointment;
  message?: string;
}

export interface TimeSlot {
  start: Date;
  end: Date;
}

/**
 * Check if two time slots overlap
 */
export function doTimeSlotsOverlap(slot1: TimeSlot, slot2: TimeSlot): boolean {
  return slot1.start < slot2.end && slot2.start < slot1.end;
}

/**
 * Create a time slot from appointment data
 */
export function createTimeSlot(appointmentDate: Date, durationInMinutes: number): TimeSlot {
  const start = new Date(appointmentDate);
  const end = new Date(start.getTime() + durationInMinutes * 60 * 1000);
  return { start, end };
}

/**
 * Check for appointment conflicts with existing appointments
 */
export function checkAppointmentConflicts(
  newAppointment: {
    appointmentDate: Date;
    durationInMinutes: number;
    practitionerId: string;
    id?: string; // For editing existing appointments
  },
  existingAppointments: Appointment[]
): AppointmentConflict {
  const newSlot = createTimeSlot(newAppointment.appointmentDate, newAppointment.durationInMinutes);
  
  // Filter appointments for the same practitioner on the same day
  const samePractitionerAppointments = existingAppointments.filter(apt => {
    // Skip the appointment being edited
    if (newAppointment.id && apt.id === newAppointment.id) {
      return false;
    }
    
    // Only check appointments for the same practitioner
    if (apt.practitioner.id !== newAppointment.practitionerId) {
      return false;
    }
    
    // Only check appointments that are not cancelled
    if (apt.status === 'cancelled') {
      return false;
    }
    
    // Only check appointments on the same day
    const aptDate = new Date(apt.appointmentDate);
    const newDate = newAppointment.appointmentDate;
    return (
      aptDate.getFullYear() === newDate.getFullYear() &&
      aptDate.getMonth() === newDate.getMonth() &&
      aptDate.getDate() === newDate.getDate()
    );
  });
  
  // Check for overlaps
  for (const existingApt of samePractitionerAppointments) {
    const existingSlot = createTimeSlot(
      new Date(existingApt.appointmentDate),
      existingApt.durationInMinutes
    );
    
    if (doTimeSlotsOverlap(newSlot, existingSlot)) {
      return {
        hasConflict: true,
        conflictingAppointment: existingApt,
        message: `This time slot conflicts with an existing appointment for ${existingApt.patient.fullName} from ${formatTime(existingSlot.start)} to ${formatTime(existingSlot.end)}.`
      };
    }
  }
  
  return { hasConflict: false };
}

/**
 * Format time for display
 */
export function formatTime(date: Date): string {
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
}

/**
 * Format date for display
 */
export function formatDate(date: Date): string {
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Get available time slots for a practitioner on a given date
 */
export function getAvailableTimeSlots(
  date: Date,
  practitionerId: string,
  durationInMinutes: number,
  existingAppointments: Appointment[],
  businessHours: { start: number; end: number } = { start: 9, end: 17 } // 9 AM to 5 PM
): TimeSlot[] {
  const availableSlots: TimeSlot[] = [];
  
  // Get appointments for the practitioner on the given date
  const dayAppointments = existingAppointments.filter(apt => {
    if (apt.practitioner.id !== practitionerId || apt.status === 'cancelled') {
      return false;
    }
    
    const aptDate = new Date(apt.appointmentDate);
    return (
      aptDate.getFullYear() === date.getFullYear() &&
      aptDate.getMonth() === date.getMonth() &&
      aptDate.getDate() === date.getDate()
    );
  });
  
  // Sort appointments by time
  dayAppointments.sort((a, b) => 
    new Date(a.appointmentDate).getTime() - new Date(b.appointmentDate).getTime()
  );
  
  // Create time slots from business hours
  const startTime = new Date(date);
  startTime.setHours(businessHours.start, 0, 0, 0);
  
  const endTime = new Date(date);
  endTime.setHours(businessHours.end, 0, 0, 0);
  
  let currentTime = new Date(startTime);
  
  while (currentTime.getTime() + durationInMinutes * 60 * 1000 <= endTime.getTime()) {
    const slotEnd = new Date(currentTime.getTime() + durationInMinutes * 60 * 1000);
    const proposedSlot = { start: new Date(currentTime), end: slotEnd };
    
    // Check if this slot conflicts with any existing appointment
    const hasConflict = dayAppointments.some(apt => {
      const existingSlot = createTimeSlot(
        new Date(apt.appointmentDate),
        apt.durationInMinutes
      );
      return doTimeSlotsOverlap(proposedSlot, existingSlot);
    });
    
    if (!hasConflict) {
      availableSlots.push(proposedSlot);
    }
    
    // Move to next 15-minute interval
    currentTime.setMinutes(currentTime.getMinutes() + 15);
  }
  
  return availableSlots;
}

/**
 * Suggest alternative time slots when there's a conflict
 */
export function suggestAlternativeSlots(
  originalDate: Date,
  practitionerId: string,
  durationInMinutes: number,
  existingAppointments: Appointment[],
  maxSuggestions: number = 5
): TimeSlot[] {
  const suggestions: TimeSlot[] = [];
  
  // Try the same day first
  const sameDaySlots = getAvailableTimeSlots(
    originalDate,
    practitionerId,
    durationInMinutes,
    existingAppointments
  );
  
  suggestions.push(...sameDaySlots.slice(0, maxSuggestions));
  
  // If we need more suggestions, try the next few days
  if (suggestions.length < maxSuggestions) {
    for (let dayOffset = 1; dayOffset <= 7 && suggestions.length < maxSuggestions; dayOffset++) {
      const nextDay = new Date(originalDate);
      nextDay.setDate(nextDay.getDate() + dayOffset);
      
      // Skip weekends (optional - can be configured)
      if (nextDay.getDay() === 0 || nextDay.getDay() === 6) {
        continue;
      }
      
      const nextDaySlots = getAvailableTimeSlots(
        nextDay,
        practitionerId,
        durationInMinutes,
        existingAppointments
      );
      
      const remainingSlots = maxSuggestions - suggestions.length;
      suggestions.push(...nextDaySlots.slice(0, remainingSlots));
    }
  }
  
  return suggestions;
}

/**
 * Validate appointment timing rules
 */
export function validateAppointmentTiming(appointmentDate: Date): {
  isValid: boolean;
  message?: string;
} {
  const now = new Date();
  const appointmentTime = new Date(appointmentDate);
  
  // Check if appointment is in the past (allow up to 1 hour in the past for flexibility)
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
  if (appointmentTime < oneHourAgo) {
    return {
      isValid: false,
      message: 'Appointments cannot be scheduled more than 1 hour in the past.'
    };
  }
  
  // Check if appointment is too far in the future (e.g., 1 year)
  const oneYearFromNow = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
  if (appointmentTime > oneYearFromNow) {
    return {
      isValid: false,
      message: 'Appointments cannot be scheduled more than 1 year in advance.'
    };
  }
  
  // Check if appointment is during business hours (9 AM to 5 PM)
  const hour = appointmentTime.getHours();
  if (hour < 9 || hour >= 17) {
    return {
      isValid: false,
      message: 'Appointments must be scheduled during business hours (9 AM to 5 PM).'
    };
  }
  
  // Check if appointment is on a weekend
  const dayOfWeek = appointmentTime.getDay();
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return {
      isValid: false,
      message: 'Appointments cannot be scheduled on weekends.'
    };
  }
  
  return { isValid: true };
}

/**
 * Calculate appointment end time
 */
export function calculateEndTime(startTime: Date, durationInMinutes: number): Date {
  return new Date(startTime.getTime() + durationInMinutes * 60 * 1000);
}

/**
 * Check if two appointments are on the same day
 */
export function isSameDay(date1: Date, date2: Date): boolean {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
}
