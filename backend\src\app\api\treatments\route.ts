import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../lib/payload-auth-middleware';

/**
 * GET /api/treatments - Get all treatments
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Extract query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const page = parseInt(url.searchParams.get('page') || '1');

    // Fetch treatments from Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'treatments',
      'find',
      {
        limit,
        page,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching treatments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch treatments' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/treatments - Create a new treatment
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const treatmentData = await request.json();

    // Create treatment in Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'treatments',
      'create',
      {
        data: treatmentData,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error creating treatment:', error);
    return NextResponse.json(
      { error: 'Failed to create treatment' },
      { status: 500 }
    );
  }
}
