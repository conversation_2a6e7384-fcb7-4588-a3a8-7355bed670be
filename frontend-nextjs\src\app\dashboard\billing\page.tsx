import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import { BillingTabs } from '@/components/billing/billing-tabs';
import { IconReceipt } from '@tabler/icons-react';
import { t } from '@/lib/translations';

export default async function BillingPage() {
  const { userId } = await auth();

  if (!userId) {
    return redirect('/auth/sign-in');
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-4'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight flex items-center gap-2'>
              <IconReceipt className='size-6' />
              财务管理
            </h2>
            <p className='text-muted-foreground'>
              管理账单、支付记录、收据和财务报表
            </p>
          </div>
        </div>

        {/* Billing Tabs - Client Component */}
        <BillingTabs />
      </div>
    </PageContainer>
  );
}
