# 账单系统组件使用指南

## 概述

本文档描述了账单管理系统中所有React组件的使用方法、属性和示例。

## 核心组件

### BillingTabs - 主要标签页界面

主要的账单管理界面，包含账单管理、预约生成、收据管理和财务报表四个标签页。

```tsx
import { BillingTabs } from '@/components/billing/billing-tabs';

<BillingTabs 
  defaultTab="bills"     // 默认显示的标签页
  className="w-full"     // 自定义样式类
/>
```

#### 属性
- `defaultTab?: string` - 默认显示的标签页 ('bills' | 'generate' | 'receipts' | 'reports')
- `className?: string` - 自定义CSS类名

### BillingList - 账单列表管理

显示账单列表，支持搜索、筛选、分页和各种账单操作。

```tsx
import { BillingList } from '@/components/billing/billing-list';

<BillingList />
```

#### 功能特性
- ✅ 高级搜索和筛选
- ✅ 分页显示
- ✅ 账单状态管理
- ✅ 支付处理
- ✅ 权限控制

### BillForm - 账单创建/编辑表单

用于创建新账单或编辑现有账单的表单组件。

```tsx
import { BillForm } from '@/components/billing/bill-form';

<BillForm
  bill={existingBill}           // 编辑时传入现有账单
  patients={patientsArray}      // 患者列表
  appointments={appointmentsArray} // 预约列表
  treatments={treatmentsArray}  // 治疗项目列表
  onSuccess={(bill) => {        // 成功回调
    console.log('账单已保存:', bill);
  }}
  onCancel={() => {             // 取消回调
    console.log('用户取消操作');
  }}
  isOpen={true}                 // 是否显示
/>
```

#### 属性
- `bill?: Bill` - 编辑时的现有账单对象
- `patients?: Patient[]` - 可选择的患者列表
- `appointments?: Appointment[]` - 可关联的预约列表
- `treatments?: Treatment[]` - 可选择的治疗项目
- `onSuccess?: (bill: Bill) => void` - 保存成功回调
- `onCancel?: () => void` - 取消操作回调
- `isOpen?: boolean` - 是否显示表单

### PaymentForm - 支付处理表单

处理账单支付的表单组件，支持多种支付方式。

```tsx
import { PaymentForm } from '@/components/billing/payment-form';

<PaymentForm
  bill={billObject}             // 要支付的账单
  onSuccess={(payment) => {     // 支付成功回调
    console.log('支付成功:', payment);
  }}
  onCancel={() => {             // 取消支付回调
    console.log('用户取消支付');
  }}
  isOpen={true}                 // 是否显示
/>
```

#### 支持的支付方式
- 现金 (cash)
- 银行卡 (card)
- 微信支付 (wechat)
- 支付宝 (alipay)
- 银行转账 (transfer)
- 分期付款 (installment)

### Receipt - 收据组件

显示和打印收据的组件。

```tsx
import { Receipt } from '@/components/billing/receipt';

<Receipt
  payment={paymentObject}       // 支付记录
  bill={billObject}            // 相关账单 (可选)
  clinicInfo={{                // 诊所信息 (可选)
    name: '美丽诊所',
    address: '北京市朝阳区美丽街123号',
    phone: '010-12345678',
    email: '<EMAIL>'
  }}
  ref={receiptRef}             // 用于打印的引用
/>
```

### BillFilters - 高级筛选组件

提供账单的高级搜索和筛选功能。

```tsx
import { BillFilters } from '@/components/billing/bill-filters';

<BillFilters
  filters={currentFilters}      // 当前筛选条件
  onFiltersChange={(filters) => { // 筛选条件变化回调
    setCurrentFilters(filters);
  }}
  patients={patientsArray}      // 患者列表用于筛选
  className="mb-4"              // 自定义样式
/>
```

#### 筛选选项
- 搜索关键词
- 账单状态
- 账单类型
- 患者选择
- 日期范围
- 金额范围

### BillStatusManager - 账单状态管理

管理账单状态转换的组件。

```tsx
import { BillStatusManager } from '@/components/billing/bill-status-manager';

<BillStatusManager
  bill={billObject}             // 要管理的账单
  onStatusUpdate={(updatedBill) => { // 状态更新回调
    console.log('状态已更新:', updatedBill);
  }}
  trigger={<CustomButton />}    // 自定义触发按钮 (可选)
/>
```

#### 状态转换规则
- 草稿 → 已发送、已取消
- 已发送 → 已确认、已取消
- 已确认 → 已支付、已取消
- 已支付 → 无转换
- 已取消 → 无转换

### AppointmentToBill - 预约生成账单

从已完成的预约自动生成账单的组件。

```tsx
import { AppointmentToBill } from '@/components/billing/appointment-to-bill';

<AppointmentToBill
  onBillGenerated={(bill) => {  // 账单生成成功回调
    console.log('账单已生成:', bill);
  }}
  className="w-full"            // 自定义样式
/>
```

### FinancialDashboard - 财务报表仪表板

显示详细财务报表和分析的组件。

```tsx
import { FinancialDashboard } from '@/components/billing/financial-dashboard';

<FinancialDashboard />
```

#### 包含的报表
- 日收入统计
- 月度收入趋势
- 支付方式分布
- 应收账款分析
- 逾期账单详情

### FinancialSummary - 财务摘要

显示关键财务指标的摘要组件。

```tsx
import { FinancialSummary } from '@/components/billing/financial-summary';

<FinancialSummary />
```

#### 显示指标
- 今日收入
- 本月收入
- 待收金额
- 逾期金额

## 对话框组件

### BillDialog - 账单对话框

在对话框中显示账单表单。

```tsx
import { BillDialog } from '@/components/billing/bill-dialog';

<BillDialog
  bill={billToEdit}             // 要编辑的账单 (可选)
  isOpen={dialogOpen}           // 是否打开对话框
  onClose={() => setDialogOpen(false)} // 关闭回调
  onSuccess={(bill) => {        // 成功回调
    console.log('账单操作成功:', bill);
    setDialogOpen(false);
  }}
/>
```

### PaymentDialog - 支付对话框

在对话框中显示支付表单。

```tsx
import { PaymentDialog } from '@/components/billing/payment-dialog';

<PaymentDialog
  bill={billToPay}              // 要支付的账单
  isOpen={dialogOpen}           // 是否打开对话框
  onClose={() => setDialogOpen(false)} // 关闭回调
  onSuccess={(payment) => {     // 支付成功回调
    console.log('支付成功:', payment);
    setDialogOpen(false);
  }}
/>
```

### ReceiptDialog - 收据对话框

在对话框中显示收据，支持打印和下载。

```tsx
import { ReceiptDialog } from '@/components/billing/receipt-dialog';

<ReceiptDialog
  payment={paymentObject}       // 支付记录
  bill={billObject}            // 相关账单 (可选)
  isOpen={dialogOpen}           // 是否打开对话框
  onClose={() => setDialogOpen(false)} // 关闭回调
/>
```

## 工具组件

### GenerateBillButton - 快速生成账单按钮

用于从预约快速生成账单的按钮组件。

```tsx
import { GenerateBillButton } from '@/components/billing/generate-bill-button';

<GenerateBillButton
  appointment={appointmentObject} // 预约对象
  onBillGenerated={(bill) => {   // 生成成功回调
    console.log('账单已生成:', bill);
  }}
  disabled={false}               // 是否禁用
  variant="default"              // 按钮样式
  size="default"                 // 按钮大小
  className="custom-class"       // 自定义样式
/>
```

### ReceiptManager - 收据管理

管理和查看所有收据的组件。

```tsx
import { ReceiptManager } from '@/components/billing/receipt-manager';

<ReceiptManager className="w-full" />
```

## 验证组件

### ValidationFeedback - 验证反馈

显示表单验证结果的组件。

```tsx
import { ValidationFeedback } from '@/components/ui/validation-feedback';

<ValidationFeedback
  result={validationResult}     // 验证结果对象
  showWarnings={true}           // 是否显示警告
  compact={false}               // 是否紧凑显示
  className="mt-2"              // 自定义样式
/>
```

### FieldValidationFeedback - 字段验证反馈

显示单个字段验证状态的组件。

```tsx
import { FieldValidationFeedback } from '@/components/ui/validation-feedback';

<FieldValidationFeedback
  error="此字段为必填项"        // 错误消息
  warning="建议检查输入"        // 警告消息
  success={true}               // 是否验证成功
  className="mt-1"             // 自定义样式
/>
```

## 使用示例

### 完整的账单管理页面

```tsx
import { BillingTabs } from '@/components/billing/billing-tabs';

export default function BillingPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">财务管理</h1>
        <p className="text-muted-foreground">
          管理账单、支付记录、收据和财务报表
        </p>
      </div>
      
      <BillingTabs defaultTab="bills" />
    </div>
  );
}
```

### 自定义账单列表

```tsx
import { useState } from 'react';
import { BillingList } from '@/components/billing/billing-list';
import { BillDialog } from '@/components/billing/bill-dialog';

export function CustomBillingPage() {
  const [showNewBillDialog, setShowNewBillDialog] = useState(false);

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">账单管理</h2>
        <Button onClick={() => setShowNewBillDialog(true)}>
          新建账单
        </Button>
      </div>
      
      <BillingList />
      
      <BillDialog
        isOpen={showNewBillDialog}
        onClose={() => setShowNewBillDialog(false)}
        onSuccess={(bill) => {
          console.log('新账单创建成功:', bill);
          setShowNewBillDialog(false);
        }}
      />
    </div>
  );
}
```

## 权限控制

所有组件都集成了权限控制，使用 `PermissionGate` 组件包装需要权限的功能：

```tsx
import { PermissionGate } from '@/lib/role-context';

<PermissionGate permission="canCreateBills">
  <Button onClick={handleCreateBill}>
    创建账单
  </Button>
</PermissionGate>
```

## 样式定制

所有组件都支持通过 `className` 属性进行样式定制，使用 Tailwind CSS 类名：

```tsx
<BillingList className="bg-white rounded-lg shadow-md p-6" />
```

## 错误处理

组件内置了错误处理机制，会自动显示用户友好的错误消息：

```tsx
// 组件会自动处理API错误并显示Toast通知
<PaymentForm
  bill={bill}
  onSuccess={(payment) => {
    // 成功处理
  }}
  // 错误会自动处理，无需额外代码
/>
```
