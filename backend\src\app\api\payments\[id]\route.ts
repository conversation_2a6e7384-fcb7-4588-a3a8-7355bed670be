import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware';

/**
 * GET /api/payments/[id] - Get a specific payment
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Fetch payment from Payload CMS with relationships
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'payments',
      'findByID',
      {
        id,
        depth: 3, // Include bill, patient, receivedBy, and nested relationships
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching payment:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payment' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/payments/[id] - Update a specific payment
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const updateData = await request.json();

    // Validate amount if provided
    if (updateData.amount !== undefined && updateData.amount <= 0) {
      return NextResponse.json(
        { error: 'Payment amount must be greater than 0' },
        { status: 400 }
      );
    }

    // Update payment in Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'payments',
      'update',
      {
        id,
        data: updateData,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating payment:', error);
    return NextResponse.json(
      { error: 'Failed to update payment' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/payments/[id] - Delete a specific payment
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has permission to delete (only admin)
    if (authContext.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Delete payment from Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'payments',
      'delete',
      {
        id,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error deleting payment:', error);
    return NextResponse.json(
      { error: 'Failed to delete payment' },
      { status: 500 }
    );
  }
}
