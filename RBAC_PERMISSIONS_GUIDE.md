# Role-Based Access Control (RBAC) Permissions Guide

## 🎯 Overview

This document defines the granular permissions for each user role in the medical aesthetics clinic management system. These permissions ensure data privacy, operational security, and proper workflow management.

## 👥 User Roles & Permissions

### 🔑 Administrator Role (`admin`)
**Purpose**: Full system management and oversight
**Access Level**: Complete administrative control

#### Permissions:
- **Users Collection**: Full CRUD access
  - Create, read, update, delete staff accounts
  - Assign and modify user roles
  - View all user activity and login history
- **Patients Collection**: Full CRUD access
  - Create, read, update, delete patient records
  - Access all patient information including medical notes
  - Manage patient photos and sensitive data
- **Appointments Collection**: Full CRUD access
  - Create, read, update, delete all appointments
  - View appointments for all practitioners
  - Manage appointment scheduling and conflicts
- **Treatments Collection**: Full CRUD access
  - Create, read, update, delete treatment definitions
  - Manage pricing and duration settings
  - Configure treatment categories and descriptions
- **Analytics & Reporting**: Full access
  - View business intelligence dashboards
  - Generate reports and analytics
  - Access financial and operational metrics

### 👩‍⚕️ Doctor Role (`doctor`)
**Purpose**: Medical treatment delivery and patient care
**Access Level**: Patient care focused with medical data access

#### Permissions:
- **Users Collection**: Read-only access
  - View other staff members (for appointment coordination)
  - Cannot create, update, or delete user accounts
- **Patients Collection**: Selective access
  - **Read**: Full access to all patient information including medical notes
  - **Update**: Can update medical notes field only
  - **Create**: Cannot create new patient records
  - **Delete**: Cannot delete patient records
- **Appointments Collection**: Own appointments only
  - **Read**: Only appointments where `practitioner.id = user.id`
  - **Update**: Can update status, notes, and medical details for own appointments
  - **Create**: Cannot create new appointments
  - **Delete**: Cannot delete appointments
- **Treatments Collection**: Read-only access
  - View treatment definitions and pricing
  - Cannot modify treatment settings
- **Analytics & Reporting**: Limited access
  - View own appointment history and patient outcomes
  - Cannot access business or financial metrics

### 🏥 Front-desk Role (`front-desk`)
**Purpose**: Patient management and appointment scheduling
**Access Level**: Administrative operations without medical data access

#### Permissions:
- **Users Collection**: Read-only access
  - View staff members for appointment assignment
  - Cannot create, update, or delete user accounts
- **Patients Collection**: Administrative access (excluding medical data)
  - **Read**: Full access except `medicalNotes` field (hidden/read-only)
  - **Create**: Can create new patient records
  - **Update**: Can update contact info, demographics, photos (not medical notes)
  - **Delete**: Can delete patient records (with confirmation)
- **Appointments Collection**: Full scheduling access
  - **Read**: View all appointments for all practitioners
  - **Create**: Can create appointments for any doctor
  - **Update**: Can reschedule, update details, change status
  - **Delete**: Can cancel/delete appointments
- **Treatments Collection**: Limited access
  - **Read**: View all treatments for appointment scheduling
  - **Update**: Can adjust pricing for specific appointments (not default pricing)
  - **Create/Delete**: Cannot create or delete treatment definitions
- **Analytics & Reporting**: Operational access
  - View scheduling metrics and appointment statistics
  - Cannot access financial or business intelligence data

## 🔒 Field-Level Security

### Medical Notes Protection
- **Field**: `patients.medicalNotes`
- **Admin**: Full read/write access
- **Doctor**: Full read/write access
- **Front-desk**: Field is hidden or read-only (no access to sensitive medical information)

### Financial Data Protection
- **Fields**: Treatment pricing, appointment costs, revenue metrics
- **Admin**: Full access to all financial data
- **Doctor**: Can view treatment prices, cannot access revenue/business metrics
- **Front-desk**: Can view and adjust appointment pricing, limited financial access

### User Management Protection
- **Collection**: `users`
- **Admin**: Full CRUD access for staff management
- **Doctor/Front-desk**: Read-only access for operational needs (seeing who's available for appointments)

## 🛡️ Implementation Strategy

### Backend (Payload CMS)
```typescript
// Example access control function
access: {
  read: ({ req: { user } }) => {
    if (!user) return false;
    if (user.role === 'admin') return true;
    if (user.role === 'doctor') return { practitioner: { equals: user.id } };
    if (user.role === 'front-desk') return true;
    return false;
  }
}
```

### Frontend (React Components)
```typescript
// Example role checking
const canEditMedicalNotes = user.role === 'admin' || user.role === 'doctor';
const canCreateAppointments = user.role === 'admin' || user.role === 'front-desk';
```

## 🚨 Security Considerations

### Data Privacy
- Medical notes are protected under HIPAA-like regulations
- Only medical staff (doctors) and administrators should access sensitive medical data
- Front-desk staff handle administrative data only

### Operational Security
- Doctors can only manage their own appointments to prevent scheduling conflicts
- Front-desk staff can manage all appointments for operational efficiency
- User management is restricted to administrators only

### Audit Trail
- All access and modifications should be logged
- Role changes should require administrator approval
- Sensitive data access should be tracked for compliance

## 📋 Testing Checklist

### Admin Role Testing
- [ ] Can create/edit/delete users
- [ ] Can access all patient data including medical notes
- [ ] Can manage all appointments and treatments
- [ ] Can view all analytics and reports

### Doctor Role Testing
- [ ] Can only see own appointments
- [ ] Can read/write patient medical notes
- [ ] Cannot create new patients or appointments
- [ ] Cannot access business metrics

### Front-desk Role Testing
- [ ] Can create patients and appointments
- [ ] Cannot see medical notes field
- [ ] Can manage all appointments for scheduling
- [ ] Cannot access sensitive financial data

## 🔄 Future Enhancements

### Advanced Permissions
- Department-based access (if clinic expands)
- Time-based access restrictions
- Location-based permissions for multi-site clinics

### Compliance Features
- HIPAA audit logging
- Data retention policies
- Consent management integration

---

**Implementation Priority**: Critical for production deployment
**Security Level**: High - affects patient data privacy and operational security
