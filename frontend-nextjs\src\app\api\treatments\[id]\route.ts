import { NextRequest } from 'next/server';
import { withAuthentication, createSuccessResponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';
import { createPayloadClient } from '@/lib/payload-client';

export const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    const data = await payloadClient.getTreatment(params.id);
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error fetching treatment:', error);
    return createErrorResponse('Failed to fetch treatment');
  }
});

export const PATCH = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    const body = await request.json();
    
    const data = await payloadClient.updateTreatment(params.id, body);
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error updating treatment:', error);
    return createErrorResponse('Failed to update treatment');
  }
});

export const DELETE = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    await payloadClient.deleteTreatment(params.id);
    
    return createSuccessResponse(null, 204);
  } catch (error) {
    console.error('Error deleting treatment:', error);
    return createErrorResponse('Failed to delete treatment');
  }
});
