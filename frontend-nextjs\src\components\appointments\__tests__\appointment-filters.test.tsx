import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render } from '@/test/utils'
import { AppointmentFiltersComponent, AppointmentFilters } from '../appointment-filters'

describe('AppointmentFiltersComponent', () => {
  const mockOnFiltersChange = vi.fn()
  const defaultFilters: AppointmentFilters = {
    search: '',
    status: 'all',
    dateFrom: undefined,
    dateTo: undefined,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders all filter controls', () => {
    render(
      <AppointmentFiltersComponent
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    expect(screen.getByLabelText(/search/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/status/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/from date/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/to date/i)).toBeInTheDocument()
  })

  it('calls onFiltersChange when search input changes', async () => {
    const user = userEvent.setup()
    
    render(
      <AppointmentFiltersComponent
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    const searchInput = screen.getByLabelText(/search/i)
    await user.type(searchInput, 'Alice')

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      ...defaultFilters,
      search: 'Alice',
    })
  })

  it('calls onFiltersChange when status filter changes', async () => {
    const user = userEvent.setup()
    
    render(
      <AppointmentFiltersComponent
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    const statusSelect = screen.getByDisplayValue('All statuses')
    await user.click(statusSelect)
    
    const scheduledOption = screen.getByText('Scheduled')
    await user.click(scheduledOption)

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      ...defaultFilters,
      status: 'scheduled',
    })
  })

  it('shows clear all button when filters are active', () => {
    const filtersWithValues: AppointmentFilters = {
      search: 'Alice',
      status: 'scheduled',
      dateFrom: new Date(),
      dateTo: new Date(),
    }

    render(
      <AppointmentFiltersComponent
        filters={filtersWithValues}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    expect(screen.getByRole('button', { name: /clear all/i })).toBeInTheDocument()
  })

  it('hides clear all button when no filters are active', () => {
    render(
      <AppointmentFiltersComponent
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    expect(screen.queryByRole('button', { name: /clear all/i })).not.toBeInTheDocument()
  })

  it('clears all filters when clear all button is clicked', async () => {
    const user = userEvent.setup()
    const filtersWithValues: AppointmentFilters = {
      search: 'Alice',
      status: 'scheduled',
      dateFrom: new Date(),
      dateTo: new Date(),
    }

    render(
      <AppointmentFiltersComponent
        filters={filtersWithValues}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    const clearButton = screen.getByRole('button', { name: /clear all/i })
    await user.click(clearButton)

    expect(mockOnFiltersChange).toHaveBeenCalledWith(defaultFilters)
  })

  it('displays current filter values', () => {
    const filtersWithValues: AppointmentFilters = {
      search: 'Alice Johnson',
      status: 'scheduled',
      dateFrom: undefined,
      dateTo: undefined,
    }

    render(
      <AppointmentFiltersComponent
        filters={filtersWithValues}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    expect(screen.getByDisplayValue('Alice Johnson')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Scheduled')).toBeInTheDocument()
  })

  it('opens date picker when date button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <AppointmentFiltersComponent
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    const fromDateButton = screen.getByText('Pick a date')
    await user.click(fromDateButton)

    // Calendar should open (this would need more specific testing with calendar component)
    expect(screen.getByRole('dialog')).toBeInTheDocument()
  })

  it('prevents selecting to date before from date', () => {
    const filtersWithFromDate: AppointmentFilters = {
      search: '',
      status: '',
      dateFrom: new Date('2024-07-10'),
      dateTo: undefined,
    }

    render(
      <AppointmentFiltersComponent
        filters={filtersWithFromDate}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    // This would require testing the calendar component's disabled dates
    // For now, we just verify the component renders with the from date
    expect(screen.getByText('Jul 10, 2024')).toBeInTheDocument()
  })

  it('handles date selection correctly', async () => {
    const user = userEvent.setup()
    
    render(
      <AppointmentFiltersComponent
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    // This would require proper calendar interaction testing
    // For now, verify the date picker buttons exist
    const dateButtons = screen.getAllByText('Pick a date')
    expect(dateButtons).toHaveLength(2) // From and To date buttons
  })
})
