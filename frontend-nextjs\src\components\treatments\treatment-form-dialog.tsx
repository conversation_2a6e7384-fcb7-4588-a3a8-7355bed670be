'use client'

import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { treatmentsApi } from '@/lib/api'
import { Treatment } from '@/types/clinic'
import { toast } from 'sonner'
import { t } from '@/lib/translations'

const treatmentSchema = z.object({
  name: z.string()
    .min(2, '治疗名称至少需要2个字符')
    .max(100, '治疗名称不能超过100个字符')
    .regex(/^[a-zA-Z0-9\s\-&()]+$/, '治疗名称只能包含字母、数字、空格、连字符和括号'),
  description: z.string()
    .max(1000, '描述不能超过1000个字符')
    .optional(),
  defaultPrice: z.number()
    .min(0, '价格必须为0元或更高')
    .max(10000, '价格不能超过10,000元')
    .multipleOf(0.01, '价格必须是有效的货币金额（例如：99.99）'),
  defaultDurationInMinutes: z.number()
    .min(5, '时长至少需要5分钟')
    .max(480, '时长不能超过8小时（480分钟）')
    .int('时长必须是整数分钟'),
})

type TreatmentFormData = z.infer<typeof treatmentSchema>

interface TreatmentFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  treatment?: Treatment
  onSuccess?: () => void
}

export function TreatmentFormDialog({
  open,
  onOpenChange,
  treatment,
  onSuccess,
}: TreatmentFormDialogProps) {
  const [loading, setLoading] = useState(false)

  const isEditing = !!treatment

  const form = useForm<TreatmentFormData>({
    resolver: zodResolver(treatmentSchema),
    defaultValues: {
      name: treatment?.name || '',
      description: treatment?.description || '',
      defaultPrice: treatment?.defaultPrice || 0,
      defaultDurationInMinutes: treatment?.defaultDurationInMinutes || 30,
    },
  })

  // Reset form when treatment changes or dialog opens
  React.useEffect(() => {
    if (open) {
      form.reset({
        name: treatment?.name || '',
        description: treatment?.description || '',
        defaultPrice: treatment?.defaultPrice || 0,
        defaultDurationInMinutes: treatment?.defaultDurationInMinutes || 30,
      })
    }
  }, [open, treatment, form])

  const onSubmit = async (data: TreatmentFormData) => {
    setLoading(true)
    try {
      const treatmentData = {
        name: data.name,
        description: data.description || undefined,
        defaultPrice: data.defaultPrice,
        defaultDurationInMinutes: data.defaultDurationInMinutes,
      }

      if (isEditing) {
        await treatmentsApi.update(treatment.id, treatmentData)
        toast.success('Treatment updated successfully')
      } else {
        await treatmentsApi.create(treatmentData)
        toast.success('Treatment created successfully')
      }

      onSuccess?.()
      onOpenChange(false)
      form.reset()
    } catch (error) {
      console.error('Failed to save treatment:', error)
      toast.error(`Failed to ${isEditing ? 'update' : 'create'} treatment`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? t('treatments.editTreatment') : t('treatments.newTreatment')}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? '更新下方的治疗信息。'
              : '填写治疗详细信息以创建新服务。'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Treatment Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('treatments.form.name')} *</FormLabel>
                  <FormControl>
                    <Input placeholder={t('treatments.form.namePlaceholder')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('treatments.form.description')}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t('treatments.form.descriptionPlaceholder')}
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              {/* Default Price */}
              <FormField
                control={form.control}
                name="defaultPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('treatments.form.price')} (¥) *</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder={t('treatments.form.pricePlaceholder')}
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Default Duration */}
              <FormField
                control={form.control}
                name="defaultDurationInMinutes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('treatments.form.duration')} (分钟) *</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder={t('treatments.form.durationPlaceholder')}
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                {t('common.actions.cancel')}
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? '保存中...' : isEditing ? '更新治疗' : '创建治疗'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
