import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware';
import { Deposit, Bill } from '../../../../payload-types';

/**
 * POST /api/deposits/apply-to-bill - Apply deposit to bill payment
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const { depositId, billId, amount } = await request.json();

    if (!depositId || !billId || !amount) {
      return NextResponse.json(
        { error: 'Deposit ID, Bill ID, and amount are required' },
        { status: 400 }
      );
    }

    if (amount <= 0) {
      return NextResponse.json(
        { error: 'Amount must be greater than 0' },
        { status: 400 }
      );
    }

    // Get deposit details
    const depositResult = await makeAuthenticatedPayloadRequest(
      authContext,
      'deposits',
      'findByID',
      {
        id: depositId,
        depth: 2,
      }
    );

    if (!depositResult) {
      return NextResponse.json(
        { error: 'Deposit not found' },
        { status: 404 }
      );
    }

    // Type cast to Deposit
    const deposit = depositResult as Deposit;

    // Get bill details
    const billResult = await makeAuthenticatedPayloadRequest(
      authContext,
      'bills',
      'findByID',
      {
        id: billId,
        depth: 2,
      }
    );

    if (!billResult) {
      return NextResponse.json(
        { error: 'Bill not found' },
        { status: 404 }
      );
    }

    // Type cast to Bill
    const bill = billResult as Bill;

    // Validate deposit can be used
    if (deposit.status !== 'active') {
      return NextResponse.json(
        { error: 'Deposit is not active' },
        { status: 400 }
      );
    }

    if ((deposit.remainingAmount || 0) < amount) {
      return NextResponse.json(
        { error: 'Insufficient deposit balance' },
        { status: 400 }
      );
    }

    // Validate bill can accept payment
    if (bill.status === 'paid' || bill.status === 'cancelled') {
      return NextResponse.json(
        { error: 'Bill cannot accept payment in current status' },
        { status: 400 }
      );
    }

    if ((bill.remainingAmount || 0) < amount) {
      return NextResponse.json(
        { error: 'Payment amount exceeds remaining bill amount' },
        { status: 400 }
      );
    }

    // Update deposit
    const newUsedAmount = (deposit.usedAmount || 0) + amount;
    const newRemainingAmount = deposit.amount - newUsedAmount;

    await makeAuthenticatedPayloadRequest(
      authContext,
      'deposits',
      'update',
      {
        id: depositId,
        data: {
          usedAmount: newUsedAmount,
          remainingAmount: newRemainingAmount,
          usedDate: new Date().toISOString(),
          status: newRemainingAmount <= 0 ? 'used' : 'active',
        },
      }
    );

    // Create payment record
    const paymentData = {
      bill: billId,
      patient: typeof bill.patient === 'number' ? bill.patient : bill.patient.id,
      amount,
      paymentMethod: 'deposit',
      paymentStatus: 'completed',
      paymentDate: new Date().toISOString(),
      receivedBy: authContext.user.id,
      notes: `押金抵扣 - 押金编号: ${deposit.depositNumber}`,
      transactionId: `DEP-${depositId}`,
    };

    const payment = await makeAuthenticatedPayloadRequest(
      authContext,
      'payments',
      'create',
      {
        data: paymentData,
      }
    );

    // Update bill
    const newPaidAmount = (bill.paidAmount || 0) + amount;
    const newBillRemainingAmount = bill.totalAmount - newPaidAmount;

    await makeAuthenticatedPayloadRequest(
      authContext,
      'bills',
      'update',
      {
        id: billId,
        data: {
          paidAmount: newPaidAmount,
          remainingAmount: newBillRemainingAmount,
          status: newBillRemainingAmount <= 0 ? 'paid' : bill.status,
          paidDate: newBillRemainingAmount <= 0 ? new Date().toISOString() : bill.paidDate,
        },
      }
    );

    return NextResponse.json({
      message: 'Deposit applied to bill successfully',
      payment,
      depositRemainingAmount: newRemainingAmount,
      billRemainingAmount: newBillRemainingAmount,
    });
  } catch (error) {
    console.error('Error applying deposit to bill:', error);
    return NextResponse.json(
      { error: 'Failed to apply deposit to bill' },
      { status: 500 }
    );
  }
}
