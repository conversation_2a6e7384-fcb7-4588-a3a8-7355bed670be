# 患者管理策略 (Patient Management Strategy)

## 🎯 用户类型和访问权限

### 内部用户（需要登录）
- **管理员** - 完全访问权限
- **医生** - 查看自己的预约 + 患者医疗记录
- **前台** - 预约管理 + 患者联系信息（无医疗记录）

### 外部用户（无需登录）
- **潜在患者** - 在线预约咨询
- **现有患者** - 查看/修改自己的预约

## 🏗️ 建议的系统架构

### 1. 公共预约页面
```
/book-appointment (公开访问)
├── 选择服务类型
├── 填写基本信息
├── 选择时间
└── 确认预约
```

### 2. 患者状态管理
```typescript
type PatientStatus = 
  | 'prospect'     // 潜在患者（仅预约，未到诊）
  | 'active'       // 活跃患者（已到诊）
  | 'inactive'     // 非活跃患者（长期未到诊）
  | 'archived'     // 归档患者
```

### 3. 数据隔离策略
- **公开信息**: 姓名、电话、邮箱、预约信息
- **医疗信息**: 病历、诊断、治疗记录（仅医生可见）
- **敏感信息**: 身份证、详细地址（管理员可见）

## 🔧 实现方案

### Phase 1: 公共预约系统
1. **创建公共预约页面** (`/book-appointment`)
2. **简化预约流程** - 无需注册
3. **自动创建患者记录** - 状态为 'prospect'
4. **邮件/短信确认** - 发送预约确认

### Phase 2: 患者自助服务
1. **预约查询页面** (`/my-appointment`)
2. **通过手机号 + 验证码访问**
3. **查看/取消/重新安排预约**

### Phase 3: 高级功能
1. **患者门户** - 完整的患者自助系统
2. **在线支付** - 预约费用支付
3. **医疗记录访问** - 患者查看自己的记录

## 📋 数据库设计调整

### Patients 表增强
```sql
ALTER TABLE patients ADD COLUMN:
- status: 'prospect' | 'active' | 'inactive' | 'archived'
- source: 'online' | 'phone' | 'referral' | 'walk-in'
- consent_marketing: boolean
- last_visit_date: timestamp
- created_via: 'staff' | 'online_booking'
```

### 新增 PublicBookings 表
```sql
CREATE TABLE public_bookings (
  id: uuid PRIMARY KEY
  patient_id: uuid REFERENCES patients(id)
  appointment_id: uuid REFERENCES appointments(id)
  booking_token: string UNIQUE  -- 用于无登录访问
  verification_code: string     -- 短信/邮件验证码
  expires_at: timestamp
  status: 'pending' | 'confirmed' | 'cancelled'
)
```

## 🚀 立即可实施的改进

### 1. 患者创建流程优化
```typescript
// 当前: 只能通过管理界面创建患者
// 改进: 支持多种创建方式

type PatientCreationSource = 
  | 'admin_panel'      // 管理员手动创建
  | 'online_booking'   // 在线预约自动创建
  | 'phone_booking'    // 电话预约时创建
  | 'walk_in'          // 现场登记
```

### 2. 预约状态增强
```typescript
type AppointmentStatus = 
  | 'pending'          // 待确认（在线预约）
  | 'confirmed'        // 已确认
  | 'checked_in'       // 已签到
  | 'in_progress'      // 进行中
  | 'completed'        // 已完成
  | 'no_show'          // 未到诊
  | 'cancelled'        // 已取消
```

### 3. 权限细化
```typescript
// 前台权限
const frontDeskPermissions = [
  'view_patient_contact',      // 查看患者联系信息
  'edit_patient_contact',      // 编辑患者联系信息
  'create_appointment',        // 创建预约
  'edit_appointment',          // 编辑预约
  'view_appointment_schedule', // 查看预约安排
  // 不包括: view_medical_records
]

// 医生权限
const doctorPermissions = [
  'view_own_appointments',     // 查看自己的预约
  'view_medical_records',      // 查看医疗记录
  'edit_medical_records',      // 编辑医疗记录
  'view_patient_history',      // 查看患者历史
  // 不包括: delete_patient, manage_users
]
```

## 🎯 下一步行动计划

### 立即实施 (本周)
1. ✅ 修复新建预约按钮错误
2. 🔄 优化患者创建流程
3. 📝 添加患者状态字段
4. 🔍 实现患者搜索功能

### 短期目标 (2-4周)
1. 🌐 创建公共预约页面
2. 📧 实现预约确认邮件
3. 📱 添加手机验证功能
4. 🔐 细化权限控制

### 长期目标 (1-3个月)
1. 🏥 完整的患者门户
2. 💳 在线支付集成
3. 📊 预约分析报表
4. 🤖 自动化提醒系统

## 💡 技术实现建议

### 公共预约页面路由
```
/                          # 主页（诊所介绍）
/services                  # 服务介绍
/book-appointment          # 在线预约
/my-appointment           # 预约查询
/contact                  # 联系我们

/dashboard/*              # 内部管理系统（需登录）
```

### API 端点设计
```
# 公开 API
POST /api/public/book-appointment
GET  /api/public/verify-appointment/:token
PUT  /api/public/appointment/:token

# 内部 API
GET  /api/appointments     # 需要认证
POST /api/appointments     # 需要认证
```

这样的设计既满足了内部管理需求，又为潜在患者提供了便捷的预约渠道。
