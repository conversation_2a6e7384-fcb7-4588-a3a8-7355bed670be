import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware';

/**
 * GET /api/deposits/[id] - Get a specific deposit
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Fetch deposit from Payload CMS with relationships
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'deposits',
      'findByID',
      {
        id,
        depth: 3, // Include patient, appointment, treatment, and nested relationships
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching deposit:', error);
    return NextResponse.json(
      { error: 'Failed to fetch deposit' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/deposits/[id] - Update a specific deposit
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const updateData = await request.json();

    // Validate amounts if provided
    if (updateData.amount !== undefined && updateData.amount <= 0) {
      return NextResponse.json(
        { error: 'Deposit amount must be greater than 0' },
        { status: 400 }
      );
    }

    if (updateData.usedAmount !== undefined && updateData.usedAmount < 0) {
      return NextResponse.json(
        { error: 'Used amount cannot be negative' },
        { status: 400 }
      );
    }

    // Update deposit in Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'deposits',
      'update',
      {
        id,
        data: updateData,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating deposit:', error);
    return NextResponse.json(
      { error: 'Failed to update deposit' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/deposits/[id] - Delete a specific deposit
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has permission to delete (only admin)
    if (authContext.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Delete deposit from Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'deposits',
      'delete',
      {
        id,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error deleting deposit:', error);
    return NextResponse.json(
      { error: 'Failed to delete deposit' },
      { status: 500 }
    );
  }
}
