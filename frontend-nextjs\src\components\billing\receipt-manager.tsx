'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  IconSearch, 
  IconReceipt, 
  IconPrinter, 
  IconDownload,
  IconEye,
  IconRefresh,
  IconAlertCircle
} from '@tabler/icons-react';
import { Payment, Bill } from '@/types/clinic';
import { paymentsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';
import { ReceiptDialog } from './receipt-dialog';
import { useRole, PermissionGate } from '@/lib/role-context';
import { toast } from 'sonner';

interface ReceiptManagerProps {
  className?: string;
}

export function ReceiptManager({ className }: ReceiptManagerProps) {
  const { hasPermission } = useRole();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [showReceiptDialog, setShowReceiptDialog] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch payments with receipts
  const fetchPayments = async (search?: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await paymentsAPI.fetchPayments({
        limit: 50,
        status: 'completed', // Only show completed payments
        search: search || undefined,
      });
      
      // Filter payments that have receipt numbers
      const paymentsWithReceipts = response.docs.filter(payment => payment.receiptNumber);
      setPayments(paymentsWithReceipts);
    } catch (err) {
      console.error('Failed to fetch payments:', err);
      const errorMessage = err instanceof BillingAPIError 
        ? err.message 
        : '加载收据失败，请稍后重试。';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchPayments();
  }, []);

  // Handle search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm !== undefined) {
        fetchPayments(searchTerm);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchPayments(searchTerm);
  };

  const handleViewReceipt = (payment: Payment) => {
    setSelectedPayment(payment);
    setShowReceiptDialog(true);
  };

  const handlePrintReceipt = (payment: Payment) => {
    // This would trigger the print functionality
    setSelectedPayment(payment);
    setShowReceiptDialog(true);
    // The receipt dialog will handle the printing
  };

  const filteredPayments = payments.filter(payment =>
    payment.receiptNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    payment.paymentNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (typeof payment.patient === 'object' && 
     payment.patient.fullName.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载收据中...</p>
        </div>
      </div>
    );
  }

  if (error && payments.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <IconAlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-600 mb-2">加载失败</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={handleRefresh} variant="outline">
            <IconRefresh className="h-4 w-4 mr-2" />
            重试
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <IconReceipt className="size-6" />
            收据管理
          </h2>
          <p className="text-muted-foreground">
            查看、打印和管理支付收据
          </p>
        </div>
      </div>

      {/* Search and Actions */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2 flex-1 max-w-md">
          <div className="relative flex-1">
            <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="搜索收据编号、支付编号或患者姓名..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <IconRefresh className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
        
        <div className="text-sm text-muted-foreground">
          共 {filteredPayments.length} 张收据
        </div>
      </div>

      {/* Error banner for non-fatal errors */}
      {error && payments.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <div className="flex items-center">
            <IconAlertCircle className="h-4 w-4 text-red-500 mr-2" />
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Receipts List */}
      {filteredPayments.length === 0 ? (
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <IconReceipt className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">暂无收据</h3>
            <p className="text-muted-foreground">
              {searchTerm ? '没有找到匹配的收据' : '还没有生成任何收据'}
            </p>
          </div>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredPayments.map((payment) => (
            <Card key={payment.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-sm font-medium">
                      {payment.receiptNumber}
                    </CardTitle>
                    <CardDescription className="text-xs">
                      支付编号: {payment.paymentNumber}
                    </CardDescription>
                  </div>
                  <Badge variant={payment.paymentStatus === 'completed' ? 'default' : 'secondary'}>
                    {billingUtils.getPaymentStatusName(payment.paymentStatus)}
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="space-y-3">
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">患者:</span>
                    <span>
                      {typeof payment.patient === 'object' 
                        ? payment.patient.fullName 
                        : '未知患者'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">支付方式:</span>
                    <span>{billingUtils.getPaymentMethodName(payment.paymentMethod)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">支付日期:</span>
                    <span>{new Date(payment.paymentDate).toLocaleDateString('zh-CN')}</span>
                  </div>
                  <div className="flex justify-between font-medium">
                    <span className="text-muted-foreground">金额:</span>
                    <span className="text-green-600">
                      {billingUtils.formatCurrency(payment.amount)}
                    </span>
                  </div>
                </div>

                <div className="flex items-center gap-2 pt-2 border-t">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleViewReceipt(payment)}
                    className="flex-1"
                  >
                    <IconEye className="h-4 w-4 mr-1" />
                    查看
                  </Button>
                  <PermissionGate permission="canGenerateReceipts">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => handlePrintReceipt(payment)}
                    >
                      <IconPrinter className="h-4 w-4" />
                    </Button>
                  </PermissionGate>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Receipt Dialog */}
      <ReceiptDialog
        payment={selectedPayment}
        isOpen={showReceiptDialog}
        onClose={() => {
          setShowReceiptDialog(false);
          setSelectedPayment(null);
        }}
      />
    </div>
  );
}
