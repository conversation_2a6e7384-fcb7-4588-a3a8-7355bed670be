'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  IconReceipt, 
  IconAlertTriangle,
  IconCheck,
  IconUser,
  IconStethoscope,
  IconCurrencyYuan,
  IconCalendar
} from '@tabler/icons-react';
import { Appointment, Bill } from '@/types/clinic';
import { billsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';
import { useRole, PermissionGate } from '@/lib/role-context';
import { billingNotifications } from '@/lib/billing-notifications';

interface GenerateBillButtonProps {
  appointment: Appointment;
  onBillGenerated?: (bill: Bill) => void;
  disabled?: boolean;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
}

export function GenerateBillButton({ 
  appointment, 
  onBillGenerated, 
  disabled = false,
  variant = 'default',
  size = 'default',
  className 
}: GenerateBillButtonProps) {
  const { hasPermission } = useRole();
  const [isOpen, setIsOpen] = useState(false);
  const [billType, setBillType] = useState<string>('treatment');
  const [generating, setGenerating] = useState(false);

  // Check if user has permission to create bills
  if (!hasPermission('canCreateBills')) {
    return null;
  }

  // Check if appointment is completed
  const isCompleted = appointment.status === 'completed';

  const handleGenerateBill = async () => {
    try {
      setGenerating(true);

      const bill = await billsAPI.generateFromAppointment(appointment.id, billType);
      
      billingNotifications.bill.generateFromAppointment(
        bill,
        new Date(appointment.appointmentDate).toLocaleDateString('zh-CN')
      );

      if (onBillGenerated) {
        onBillGenerated(bill);
      }

      setIsOpen(false);
      setBillType('treatment'); // Reset to default
      
    } catch (error) {
      console.error('Failed to generate bill:', error);
      const errorMessage = error instanceof BillingAPIError 
        ? error.message 
        : undefined;
      billingNotifications.bill.createError(errorMessage);
    } finally {
      setGenerating(false);
    }
  };

  const getBillTypeDescription = (type: string) => {
    switch (type) {
      case 'treatment':
        return '用于治疗服务的标准账单';
      case 'consultation':
        return '用于咨询服务的账单';
      case 'additional':
        return '用于额外服务或材料的补充账单';
      default:
        return '';
    }
  };

  return (
    <PermissionGate permission="canCreateBills">
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button 
            variant={variant} 
            size={size} 
            disabled={disabled || !isCompleted}
            className={className}
          >
            <IconReceipt className="h-4 w-4 mr-2" />
            生成账单
          </Button>
        </DialogTrigger>
        
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <IconReceipt className="h-5 w-5" />
              生成账单
            </DialogTitle>
            <DialogDescription>
              为预约生成对应的账单
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Appointment Summary */}
            <div className="bg-muted/50 rounded-lg p-4 space-y-2">
              <h4 className="font-medium text-sm">预约信息</h4>
              
              <div className="grid grid-cols-1 gap-2 text-sm">
                <div className="flex items-center gap-2">
                  <IconCalendar className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {new Date(appointment.appointmentDate).toLocaleDateString('zh-CN')}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <IconUser className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {typeof appointment.patient === 'object' 
                      ? appointment.patient.fullName 
                      : '未知患者'}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <IconStethoscope className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {typeof appointment.treatment === 'object' 
                      ? appointment.treatment.name 
                      : '未知治疗'}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <IconCurrencyYuan className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {billingUtils.formatCurrency(appointment.price || 0)}
                  </span>
                </div>
              </div>
            </div>

            {/* Bill Type Selection */}
            <div className="space-y-2">
              <Label htmlFor="bill-type">账单类型</Label>
              <Select value={billType} onValueChange={setBillType}>
                <SelectTrigger>
                  <SelectValue placeholder="选择账单类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="treatment">治疗账单</SelectItem>
                  <SelectItem value="consultation">咨询账单</SelectItem>
                  <SelectItem value="additional">补充账单</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                {getBillTypeDescription(billType)}
              </p>
            </div>

            {/* Warning for non-completed appointments */}
            {!isCompleted && (
              <Alert variant="destructive">
                <IconAlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  只能为已完成的预约生成账单。当前预约状态: {appointment.status}
                </AlertDescription>
              </Alert>
            )}

            {/* Success info */}
            {isCompleted && (
              <Alert>
                <IconCheck className="h-4 w-4" />
                <AlertDescription>
                  账单将包含预约的治疗项目、价格和患者信息。生成后可在账单列表中进一步编辑。
                </AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2 pt-2">
              <Button
                onClick={handleGenerateBill}
                disabled={generating || !isCompleted}
                className="flex-1"
              >
                {generating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    生成中...
                  </>
                ) : (
                  <>
                    <IconReceipt className="h-4 w-4 mr-2" />
                    确认生成
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsOpen(false)}
                disabled={generating}
              >
                取消
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </PermissionGate>
  );
}
