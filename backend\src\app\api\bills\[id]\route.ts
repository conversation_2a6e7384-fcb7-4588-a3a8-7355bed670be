import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware';

/**
 * GET /api/bills/[id] - Get a specific bill
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Fetch bill from Payload CMS with relationships
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'bills',
      'findByID',
      {
        id,
        depth: 3, // Include patient, appointment, treatment, createdBy, and nested relationships
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching bill:', error);
    return NextResponse.json(
      { error: 'Failed to fetch bill' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/bills/[id] - Update a specific bill
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const updateData = await request.json();

    // Validate amounts if provided
    if (updateData.subtotal !== undefined && updateData.subtotal < 0) {
      return NextResponse.json(
        { error: 'Subtotal cannot be negative' },
        { status: 400 }
      );
    }

    if (updateData.totalAmount !== undefined && updateData.totalAmount < 0) {
      return NextResponse.json(
        { error: 'Total amount cannot be negative' },
        { status: 400 }
      );
    }

    if (updateData.paidAmount !== undefined && updateData.paidAmount < 0) {
      return NextResponse.json(
        { error: 'Paid amount cannot be negative' },
        { status: 400 }
      );
    }

    // Update bill in Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'bills',
      'update',
      {
        id,
        data: updateData,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating bill:', error);
    return NextResponse.json(
      { error: 'Failed to update bill' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/bills/[id] - Delete a specific bill
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has permission to delete (only admin)
    if (authContext.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Delete bill from Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'bills',
      'delete',
      {
        id,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error deleting bill:', error);
    return NextResponse.json(
      { error: 'Failed to delete bill' },
      { status: 500 }
    );
  }
}
