'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { BillForm } from './bill-form';
import { Bill, Patient, Appointment, Treatment } from '@/types/clinic';
import { toast } from 'sonner';

interface BillDialogProps {
  bill?: Bill | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (bill: Bill) => void;
}

export function BillDialog({ bill, isOpen, onClose, onSuccess }: BillDialogProps) {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [treatments, setTreatments] = useState<Treatment[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch required data when dialog opens
  useEffect(() => {
    if (isOpen) {
      fetchRequiredData();
    }
  }, [isOpen]);

  const fetchRequiredData = async () => {
    try {
      setLoading(true);
      
      // Fetch patients, appointments, and treatments
      // These would typically come from your API
      const [patientsResponse, appointmentsResponse, treatmentsResponse] = await Promise.all([
        fetch('/api/patients').then(res => res.json()),
        fetch('/api/appointments').then(res => res.json()),
        fetch('/api/treatments').then(res => res.json()),
      ]);

      setPatients(patientsResponse.docs || []);
      setAppointments(appointmentsResponse.docs || []);
      setTreatments(treatmentsResponse.docs || []);
    } catch (error) {
      console.error('Failed to fetch required data:', error);
      toast.error('加载数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleSuccess = (newBill: Bill) => {
    if (onSuccess) {
      onSuccess(newBill);
    }
    onClose();
  };

  const isEditing = !!bill;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? `编辑账单 - ${bill?.billNumber}` : '创建新账单'}
          </DialogTitle>
        </DialogHeader>
        
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2 text-muted-foreground">加载数据中...</span>
          </div>
        ) : (
          <BillForm
            bill={bill || undefined}
            patients={patients}
            appointments={appointments}
            treatments={treatments}
            onSuccess={handleSuccess}
            onCancel={onClose}
            isOpen={true}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
