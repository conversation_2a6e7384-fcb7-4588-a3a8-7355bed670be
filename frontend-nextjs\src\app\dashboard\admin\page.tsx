'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useRole, RoleGate } from '@/lib/role-context';
import { redirect } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { IconUsers, IconShield, IconUserCheck } from '@tabler/icons-react';
import { createPayloadClient } from '@/lib/payload-client';
import { getRoleDisplayName, getRoleBadgeColor, UserRole } from '@/lib/role-utils';
import { toast } from 'sonner';
import { t } from '@/lib/translations';

interface PayloadUser {
  id: string;
  email: string;
  role: UserRole;
  clerkId: string;
  firstName?: string;
  lastName?: string;
  createdAt: string;
  updatedAt: string;
}

export default function AdminPage() {
  const { userId, isLoaded } = useAuth();
  const { user: currentUser, refreshUser } = useRole();
  const [users, setUsers] = useState<PayloadUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updatingUserId, setUpdatingUserId] = useState<string | null>(null);

  // Redirect if not authenticated
  if (isLoaded && !userId) {
    redirect('/auth/sign-in');
  }

  // Fetch users data
  const fetchUsers = async () => {
    if (!currentUser) return;
    
    try {
      setLoading(true);
      const payloadClient = createPayloadClient(currentUser);
      const response = await payloadClient.getUsers({ limit: 100 }) as any;
      setUsers(response.docs || []);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch users:', err);
      setError('Failed to load users. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentUser && currentUser.role === 'admin') {
      fetchUsers();
    }
  }, [currentUser]);

  const handleRoleChange = async (userId: string, newRole: UserRole) => {
    if (!currentUser) return;
    
    try {
      setUpdatingUserId(userId);
      const payloadClient = createPayloadClient(currentUser);
      
      await payloadClient.updateUser(userId, { role: newRole });
      
      // Update local state
      setUsers(users.map(user => 
        user.id === userId ? { ...user, role: newRole } : user
      ));
      
      toast.success('User role updated successfully');
      
      // Refresh current user if they updated their own role
      if (currentUser.payloadUserId === userId) {
        await refreshUser();
      }
    } catch (error) {
      console.error('Failed to update user role:', error);
      toast.error('Failed to update user role');
    } finally {
      setUpdatingUserId(null);
    }
  };

  if (!isLoaded || loading) {
    return (
      <PageContainer>
        <div className='flex items-center justify-center h-64'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4'></div>
            <p className='text-muted-foreground'>加载管理面板中...</p>
          </div>
        </div>
      </PageContainer>
    );
  }

  return (
    <RoleGate 
      roles="admin" 
      fallback={
        <PageContainer>
          <div className='flex items-center justify-center h-64'>
            <div className='text-center'>
              <IconShield className='size-12 mx-auto text-muted-foreground mb-4' />
              <h3 className='text-lg font-semibold mb-2'>访问被拒绝</h3>
              <p className='text-muted-foreground'>
                您需要管理员权限才能访问此页面。
              </p>
            </div>
          </div>
        </PageContainer>
      }
    >
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-4'>
          {/* Header */}
          <div className='flex items-center justify-between'>
            <div>
              <h2 className='text-2xl font-bold tracking-tight flex items-center gap-2'>
                <IconUsers className='size-6' />
                {t('admin.title')}
              </h2>
              <p className='text-muted-foreground'>
                {t('admin.subtitle')}
              </p>
            </div>
          </div>

          {/* Current User Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <IconUserCheck className="size-5" />
                当前用户
              </CardTitle>
              <CardDescription>
                您当前的角色和权限
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div>
                  <p className="font-medium">{currentUser?.email}</p>
                  <p className="text-sm text-muted-foreground">
                    {currentUser?.firstName} {currentUser?.lastName}
                  </p>
                </div>
                <Badge className={getRoleBadgeColor(currentUser?.role || 'front-desk')}>
                  {getRoleDisplayName(currentUser?.role || 'front-desk')}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Users List */}
          {error ? (
            <Card>
              <CardContent className="pt-6">
                <div className='text-center py-8'>
                  <p className='text-red-600 mb-4'>{error}</p>
                  <Button onClick={fetchUsers}>Try Again</Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>All Users ({users.length})</CardTitle>
                <CardDescription>
                  Manage roles for all system users
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {users.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex-1">
                        <div className="flex items-center gap-3">
                          <div>
                            <p className="font-medium">{user.email}</p>
                            <p className="text-sm text-muted-foreground">
                              {user.firstName} {user.lastName}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Joined: {new Date(user.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <Badge className={getRoleBadgeColor(user.role)}>
                          {getRoleDisplayName(user.role)}
                        </Badge>
                        
                        <Select
                          value={user.role}
                          onValueChange={(newRole: UserRole) => handleRoleChange(user.id, newRole)}
                          disabled={updatingUserId === user.id}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="admin">Administrator</SelectItem>
                            <SelectItem value="doctor">Doctor</SelectItem>
                            <SelectItem value="front-desk">Front Desk</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  ))}
                  
                  {users.length === 0 && (
                    <div className="text-center py-8">
                      <IconUsers className="size-12 mx-auto text-muted-foreground mb-4" />
                      <p className="text-muted-foreground">No users found</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </PageContainer>
    </RoleGate>
  );
}
