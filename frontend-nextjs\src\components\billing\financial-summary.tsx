'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  IconTrendingUp, 
  IconTrendingDown,
  IconCash,
  IconAlertTriangle,
  IconReceipt
} from '@tabler/icons-react';
import { reportsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';
import { useRole, PermissionGate } from '@/lib/role-context';

interface FinancialSummaryData {
  todayRevenue: number;
  monthlyRevenue: number;
  outstandingAmount: number;
  overdueAmount: number;
  recentPayments: number;
}

export function FinancialSummary() {
  const { hasPermission } = useRole();
  const [data, setData] = useState<FinancialSummaryData>({
    todayRevenue: 0,
    monthlyRevenue: 0,
    outstandingAmount: 0,
    overdueAmount: 0,
    recentPayments: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (hasPermission('canViewFinancialData')) {
      fetchSummaryData();
    }
  }, [hasPermission]);

  const fetchSummaryData = async () => {
    try {
      setLoading(true);
      
      const today = new Date().toISOString().split('T')[0];
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();

      const [dailyRevenue, monthlyRevenue, outstandingBalances] = await Promise.all([
        reportsAPI.getDailyRevenue(today).catch(() => null),
        reportsAPI.getMonthlyRevenue(currentYear, currentMonth).catch(() => null),
        reportsAPI.getOutstandingBalances().catch(() => null),
      ]);

      setData({
        todayRevenue: dailyRevenue?.totalRevenue || 0,
        monthlyRevenue: monthlyRevenue?.totalRevenue || 0,
        outstandingAmount: outstandingBalances?.totalOutstanding || 0,
        overdueAmount: outstandingBalances?.overdueAmount || 0,
        recentPayments: dailyRevenue?.paymentCount || 0,
      });
    } catch (error) {
      console.error('Failed to fetch financial summary:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!hasPermission('canViewFinancialData')) {
    return null;
  }

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-muted rounded animate-pulse" />
              </CardTitle>
              <div className="h-4 w-4 bg-muted rounded animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded animate-pulse mb-1" />
              <div className="h-3 bg-muted rounded animate-pulse w-2/3" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Today's Revenue */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">今日收入</CardTitle>
          <IconCash className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {billingUtils.formatCurrency(data.todayRevenue)}
          </div>
          <p className="text-xs text-muted-foreground">
            {data.recentPayments} 笔支付
          </p>
        </CardContent>
      </Card>

      {/* Monthly Revenue */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">本月收入</CardTitle>
          <IconTrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {billingUtils.formatCurrency(data.monthlyRevenue)}
          </div>
          <p className="text-xs text-muted-foreground">
            {new Date().getFullYear()}年{new Date().getMonth() + 1}月
          </p>
        </CardContent>
      </Card>

      {/* Outstanding Amount */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">待收金额</CardTitle>
          <IconReceipt className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-600">
            {billingUtils.formatCurrency(data.outstandingAmount)}
          </div>
          <p className="text-xs text-muted-foreground">
            应收账款总额
          </p>
        </CardContent>
      </Card>

      {/* Overdue Amount */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">逾期金额</CardTitle>
          <IconAlertTriangle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">
            {billingUtils.formatCurrency(data.overdueAmount)}
          </div>
          <p className="text-xs text-muted-foreground">
            {data.overdueAmount > 0 ? '需要关注' : '无逾期账款'}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
