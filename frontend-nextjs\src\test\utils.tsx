import React, { ReactElement, createContext, useContext } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { ThemeProvider } from 'next-themes'
import { AuthenticatedUser } from '@/lib/auth-middleware'
import { UserRole, getRolePermissions, hasPermission, hasRole, RolePermissions } from '@/lib/role-utils'

// Mock Role Context for testing
interface MockRoleContextType {
  user: AuthenticatedUser | null;
  loading: boolean;
  error: string | null;
  permissions: RolePermissions | null;
  hasPermission: (permission: keyof RolePermissions) => boolean;
  hasRole: (roles: UserRole | UserRole[]) => boolean;
  refreshUser: () => Promise<void>;
}

const MockRoleContext = createContext<MockRoleContextType | undefined>(undefined);

interface MockRoleProviderProps {
  children: React.ReactNode;
  user?: AuthenticatedUser | null;
  loading?: boolean;
  error?: string | null;
}

// Mock Role<PERSON>rovider for testing
export function MockRoleProvider({
  children,
  user = createMockUser({ role: 'admin' }),
  loading = false,
  error = null
}: MockRoleProviderProps) {
  const permissions = user?.role ? getRolePermissions(user.role) : null;

  const contextValue: MockRoleContextType = {
    user,
    loading,
    error,
    permissions,
    hasPermission: (permission: keyof RolePermissions) => hasPermission(user, permission),
    hasRole: (roles: UserRole | UserRole[]) => hasRole(user, roles),
    refreshUser: async () => {},
  };

  return (
    <MockRoleContext.Provider value={contextValue}>
      {children}
    </MockRoleContext.Provider>
  );
}

// Mock useRole hook for testing
export function useMockRole(): MockRoleContextType {
  const context = useContext(MockRoleContext);
  if (context === undefined) {
    throw new Error('useMockRole must be used within a MockRoleProvider');
  }
  return context;
}

// Custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <MockRoleProvider>
        {children}
      </MockRoleProvider>
    </ThemeProvider>
  )
}

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  roleProviderProps?: Partial<MockRoleProviderProps>;
}

const customRender = (
  ui: ReactElement,
  options?: CustomRenderOptions
) => {
  const { roleProviderProps, ...renderOptions } = options || {};

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <MockRoleProvider {...roleProviderProps}>
        {children}
      </MockRoleProvider>
    </ThemeProvider>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

export * from '@testing-library/react'
export { customRender as render }

// Helper functions for testing
export const createMockAppointment = (overrides = {}) => ({
  id: 'test-appointment-1',
  appointmentDate: '2024-07-10T10:00:00.000Z',
  status: 'scheduled' as const,
  treatment: {
    id: 'test-treatment-1',
    name: 'Test Treatment',
    defaultPrice: 100,
    defaultDurationInMinutes: 30,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  price: 100,
  durationInMinutes: 30,
  patient: {
    id: 'test-patient-1',
    fullName: 'Test Patient',
    phone: '******-0100',
    email: '<EMAIL>',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  practitioner: {
    id: 'test-user-1',
    email: '<EMAIL>',
    role: 'doctor' as const,
    clerkId: 'test-clerk-id',
    firstName: 'Test',
    lastName: 'Doctor',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
  ...overrides,
})

export const createMockPatient = (overrides = {}) => ({
  id: 'test-patient-1',
  fullName: 'Test Patient',
  phone: '******-0100',
  email: '<EMAIL>',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
  ...overrides,
})

export const createMockTreatment = (overrides = {}) => ({
  id: 'test-treatment-1',
  name: 'Test Treatment',
  description: 'A test treatment',
  defaultPrice: 100,
  defaultDurationInMinutes: 30,
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
  ...overrides,
})

export const createMockUser = (overrides = {}): AuthenticatedUser => ({
  clerkId: 'test-clerk-id',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  role: 'doctor' as const,
  payloadUserId: 'test-payload-user-1',
  ...overrides,
})

// Wait for async operations to complete
export const waitForLoadingToFinish = () =>
  new Promise((resolve) => setTimeout(resolve, 0))

// Mock fetch responses
export const createMockResponse = (data: any, status = 200) => ({
  ok: status >= 200 && status < 300,
  status,
  json: () => Promise.resolve(data),
  text: () => Promise.resolve(JSON.stringify(data)),
})

// Mock Payload response format
export const createMockPayloadResponse = (docs: any[], totalDocs?: number) => ({
  docs,
  totalDocs: totalDocs ?? docs.length,
  limit: 10,
  totalPages: Math.ceil((totalDocs ?? docs.length) / 10),
  page: 1,
  pagingCounter: 1,
  hasPrevPage: false,
  hasNextPage: false,
})
