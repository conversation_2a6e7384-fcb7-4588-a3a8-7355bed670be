import type { CollectionConfig, Access } from 'payload'

export const Deposits: CollectionConfig = {
  slug: 'deposits',
  admin: {
    useAsTitle: 'depositNumber',
    defaultColumns: ['depositNumber', 'patient', 'depositType', 'amount', 'remainingAmount', 'status'],
    listSearchableFields: ['depositNumber', 'patient.fullName', 'purpose'],
  },
  access: {
    // Read: Same as Bills - Admin and Front-desk see all, Doctors see only related deposits
    read: (({ req: { user } }) => {
      if (!user) return false;
      if (user.role === 'admin' || user.role === 'front-desk') {
        return true;
      }
      if (user.role === 'doctor') {
        return {
          or: [
            {
              'appointment.practitioner': {
                equals: user.id,
              },
            },
            {
              'treatment.practitioner': {
                equals: user.id,
              },
            },
          ],
        };
      }
      return false;
    }) as Access,

    // Create: Admin and Front-desk can create deposits
    create: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin' || user.role === 'front-desk';
    },

    // Update: Admin and Front-desk can update deposits
    update: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin' || user.role === 'front-desk';
    },

    // Delete: Only Admin can delete deposits
    delete: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin';
    },
  },
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Auto-generate deposit number if not provided
        if (!data.depositNumber) {
          const now = new Date();
          const year = now.getFullYear();
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const day = String(now.getDate()).padStart(2, '0');
          const timestamp = now.getTime().toString().slice(-6);
          data.depositNumber = `DEP-${year}${month}${day}-${timestamp}`;
        }

        // Calculate remaining amount
        data.remainingAmount = (data.amount || 0) - (data.usedAmount || 0);

        // Auto-update status based on remaining amount
        if (data.remainingAmount <= 0 && data.usedAmount > 0) {
          data.status = 'used';
        } else if (data.remainingAmount > 0 && data.usedAmount > 0) {
          data.status = 'active'; // Partially used but still active
        }

        return data;
      },
    ],
  },
  fields: [
    {
      name: 'depositNumber',
      type: 'text',
      required: true,
      unique: true,
      label: '押金编号',
      admin: {
        description: '系统自动生成，格式：DEP-YYYYMMDD-XXXXXX',
        readOnly: true,
      },
    },
    
    // 关联信息
    {
      name: 'patient',
      type: 'relationship',
      relationTo: 'patients',
      required: true,
      hasMany: false,
      label: '患者',
    },
    {
      name: 'appointment',
      type: 'relationship',
      relationTo: 'appointments',
      hasMany: false,
      label: '关联预约',
      admin: {
        description: '如果押金与特定预约相关',
      },
    },
    {
      name: 'treatment',
      type: 'relationship',
      relationTo: 'treatments',
      hasMany: false,
      label: '关联治疗',
      admin: {
        description: '如果押金与特定治疗相关',
      },
    },
    
    // 押金信息
    {
      name: 'depositType',
      type: 'select',
      required: true,
      options: [
        {
          label: '治疗押金',
          value: 'treatment',
        },
        {
          label: '预约押金',
          value: 'appointment',
        },
        {
          label: '材料押金',
          value: 'material',
        },
      ],
      defaultValue: 'treatment',
      label: '押金类型',
    },
    {
      name: 'amount',
      type: 'number',
      required: true,
      label: '押金金额',
      min: 0.01,
      admin: {
        description: '押金总金额',
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        {
          label: '有效',
          value: 'active',
        },
        {
          label: '已使用',
          value: 'used',
        },
        {
          label: '已退还',
          value: 'refunded',
        },
        {
          label: '已过期',
          value: 'expired',
        },
      ],
      defaultValue: 'active',
      label: '押金状态',
    },
    
    // 使用记录
    {
      name: 'usedAmount',
      type: 'number',
      defaultValue: 0,
      label: '已使用金额',
      min: 0,
      admin: {
        description: '已使用的押金金额',
      },
    },
    {
      name: 'remainingAmount',
      type: 'number',
      label: '剩余金额',
      admin: {
        description: '剩余可用押金金额（自动计算）',
        readOnly: true,
      },
    },
    
    // 时间信息
    {
      name: 'depositDate',
      type: 'date',
      required: true,
      label: '收取日期',
      defaultValue: () => new Date().toISOString(),
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'expiryDate',
      type: 'date',
      label: '到期日期',
      admin: {
        date: {
          pickerAppearance: 'dayOnly',
        },
        description: '押金到期日期（可选）',
      },
    },
    {
      name: 'usedDate',
      type: 'date',
      label: '使用日期',
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description: '押金使用日期',
        condition: (data) => data.status === 'used' || data.usedAmount > 0,
      },
    },
    {
      name: 'refundDate',
      type: 'date',
      label: '退还日期',
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description: '押金退还日期',
        condition: (data) => data.status === 'refunded',
      },
    },
    
    // 备注
    {
      name: 'purpose',
      type: 'text',
      required: true,
      label: '押金用途',
      admin: {
        description: '押金的具体用途说明',
      },
    },
    {
      name: 'notes',
      type: 'textarea',
      label: '备注',
      admin: {
        description: '押金相关的备注信息',
      },
    },
  ],
}
