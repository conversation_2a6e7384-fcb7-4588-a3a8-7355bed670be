import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware';

/**
 * GET /api/users/[id] - Get a specific user
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Fetch user from Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'users',
      'findByID',
      {
        id,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/users/[id] - Update a specific user
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const updateData = await request.json();

    // Update user in Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'users',
      'update',
      {
        id,
        data: updateData,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/users/[id] - Delete a specific user
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Delete user from Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'users',
      'delete',
      {
        id,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}
