// Role-based access control utilities for frontend components
import { AuthenticatedUser } from './auth-middleware';

export type UserRole = 'admin' | 'front-desk' | 'doctor';

export interface RolePermissions {
  canCreatePatients: boolean;
  canEditPatients: boolean;
  canDeletePatients: boolean;
  canViewMedicalNotes: boolean;
  canEditMedicalNotes: boolean;
  canCreateAppointments: boolean;
  canEditAllAppointments: boolean;
  canEditOwnAppointments: boolean;
  canDeleteAppointments: boolean;
  canCreateTreatments: boolean;
  canEditTreatments: boolean;
  canDeleteTreatments: boolean;
  canManageUsers: boolean;
  canViewAnalytics: boolean;
  canViewFinancialData: boolean;
  // Billing and Payment Permissions
  canCreateBills: boolean;
  canEditBills: boolean;
  canDeleteBills: boolean;
  canViewAllBills: boolean;
  canViewOwnBills: boolean;
  canProcessPayments: boolean;
  canViewPayments: boolean;
  canGenerateReceipts: boolean;
  canHandleRefunds: boolean;
  canGenerateReports: boolean;
  canViewDetailedFinancials: boolean;
  canManagePaymentMethods: boolean;
}

/**
 * Get permissions for a specific user role
 */
export function getRolePermissions(role: UserRole): RolePermissions {
  switch (role) {
    case 'admin':
      return {
        canCreatePatients: true,
        canEditPatients: true,
        canDeletePatients: true,
        canViewMedicalNotes: true,
        canEditMedicalNotes: true,
        canCreateAppointments: true,
        canEditAllAppointments: true,
        canEditOwnAppointments: true,
        canDeleteAppointments: true,
        canCreateTreatments: true,
        canEditTreatments: true,
        canDeleteTreatments: true,
        canManageUsers: true,
        canViewAnalytics: true,
        canViewFinancialData: true,
        // Billing and Payment Permissions - Admin has full access
        canCreateBills: true,
        canEditBills: true,
        canDeleteBills: true,
        canViewAllBills: true,
        canViewOwnBills: true,
        canProcessPayments: true,
        canViewPayments: true,
        canGenerateReceipts: true,
        canHandleRefunds: true,
        canGenerateReports: true,
        canViewDetailedFinancials: true,
        canManagePaymentMethods: true,
      };

    case 'doctor':
      return {
        canCreatePatients: false,
        canEditPatients: false, // Can only edit medical notes
        canDeletePatients: false,
        canViewMedicalNotes: true,
        canEditMedicalNotes: true,
        canCreateAppointments: false,
        canEditAllAppointments: false,
        canEditOwnAppointments: true,
        canDeleteAppointments: false,
        canCreateTreatments: false,
        canEditTreatments: false,
        canDeleteTreatments: false,
        canManageUsers: false,
        canViewAnalytics: false,
        canViewFinancialData: false,
        // Billing and Payment Permissions - Doctor has limited access
        canCreateBills: false,
        canEditBills: false,
        canDeleteBills: false,
        canViewAllBills: false,
        canViewOwnBills: true, // Can view bills related to their appointments
        canProcessPayments: false,
        canViewPayments: true, // Can view payment status for their patients
        canGenerateReceipts: false,
        canHandleRefunds: false,
        canGenerateReports: false,
        canViewDetailedFinancials: false,
        canManagePaymentMethods: false,
      };

    case 'front-desk':
      return {
        canCreatePatients: true,
        canEditPatients: true,
        canDeletePatients: true,
        canViewMedicalNotes: false,
        canEditMedicalNotes: false,
        canCreateAppointments: true,
        canEditAllAppointments: true,
        canEditOwnAppointments: true,
        canDeleteAppointments: true,
        canCreateTreatments: false,
        canEditTreatments: false,
        canDeleteTreatments: false,
        canManageUsers: false,
        canViewAnalytics: false,
        canViewFinancialData: false,
        // Billing and Payment Permissions - Front-desk handles payments but limited financial access
        canCreateBills: true,
        canEditBills: true,
        canDeleteBills: false, // Cannot delete bills
        canViewAllBills: true,
        canViewOwnBills: true,
        canProcessPayments: true,
        canViewPayments: true,
        canGenerateReceipts: true,
        canHandleRefunds: false, // Cannot handle refunds without approval
        canGenerateReports: false, // Limited reporting access
        canViewDetailedFinancials: false,
        canManagePaymentMethods: true,
      };

    default:
      // Default to no permissions for unknown roles
      return {
        canCreatePatients: false,
        canEditPatients: false,
        canDeletePatients: false,
        canViewMedicalNotes: false,
        canEditMedicalNotes: false,
        canCreateAppointments: false,
        canEditAllAppointments: false,
        canEditOwnAppointments: false,
        canDeleteAppointments: false,
        canCreateTreatments: false,
        canEditTreatments: false,
        canDeleteTreatments: false,
        canManageUsers: false,
        canViewAnalytics: false,
        canViewFinancialData: false,
        // Billing and Payment Permissions - No access for unknown roles
        canCreateBills: false,
        canEditBills: false,
        canDeleteBills: false,
        canViewAllBills: false,
        canViewOwnBills: false,
        canProcessPayments: false,
        canViewPayments: false,
        canGenerateReceipts: false,
        canHandleRefunds: false,
        canGenerateReports: false,
        canViewDetailedFinancials: false,
        canManagePaymentMethods: false,
      };
  }
}

/**
 * Check if user has a specific permission
 */
export function hasPermission(
  user: AuthenticatedUser | null,
  permission: keyof RolePermissions
): boolean {
  if (!user || !user.role) return false;
  
  const permissions = getRolePermissions(user.role);
  return permissions[permission];
}

/**
 * Check if user has any of the specified roles
 */
export function hasRole(
  user: AuthenticatedUser | null,
  roles: UserRole | UserRole[]
): boolean {
  if (!user || !user.role) return false;
  
  const allowedRoles = Array.isArray(roles) ? roles : [roles];
  return allowedRoles.includes(user.role);
}

/**
 * Check if user can access a specific appointment (for doctors)
 */
export function canAccessAppointment(
  user: AuthenticatedUser | null,
  appointmentPractitionerId: string
): boolean {
  if (!user || !user.role) return false;
  
  // Admin and front-desk can access all appointments
  if (user.role === 'admin' || user.role === 'front-desk') {
    return true;
  }
  
  // Doctors can only access their own appointments
  if (user.role === 'doctor') {
    return user.payloadUserId === appointmentPractitionerId;
  }
  
  return false;
}

/**
 * Get user display name
 */
export function getUserDisplayName(user: AuthenticatedUser | null): string {
  if (!user) return '未知用户';

  if (user.firstName && user.lastName) {
    return `${user.firstName} ${user.lastName}`;
  }

  if (user.firstName) {
    return user.firstName;
  }

  return user.email;
}

/**
 * Get role display name
 */
export function getRoleDisplayName(role: UserRole): string {
  switch (role) {
    case 'admin':
      return '管理员';
    case 'front-desk':
      return '前台';
    case 'doctor':
      return '医生';
    default:
      return '未知角色';
  }
}

/**
 * Get role badge color for UI display
 */
export function getRoleBadgeColor(role: UserRole): string {
  switch (role) {
    case 'admin':
      return 'bg-red-100 text-red-800';
    case 'doctor':
      return 'bg-blue-100 text-blue-800';
    case 'front-desk':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

/**
 * Navigation items that should be hidden based on role
 */
export function getHiddenNavItems(role: UserRole): string[] {
  switch (role) {
    case 'admin':
      return []; // Admin can see everything
    
    case 'doctor':
      return ['treatments', 'analytics', 'users']; // Hide treatment management, analytics, user management
    
    case 'front-desk':
      return ['analytics', 'users']; // Hide analytics and user management
    
    default:
      return ['appointments', 'patients', 'treatments', 'analytics', 'users']; // Hide everything for unknown roles
  }
}

/**
 * Check if a navigation item should be visible for the user
 */
export function isNavItemVisible(
  user: AuthenticatedUser | null,
  navItem: string
): boolean {
  if (!user || !user.role) return false;
  
  const hiddenItems = getHiddenNavItems(user.role);
  return !hiddenItems.includes(navItem);
}
