import { Appointment, Patient, Treatment, User } from '@/types/clinic'

export const mockUsers: User[] = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    role: 'admin',
    clerkId: 'clerk-user-1',
    firstName: 'Admin',
    lastName: 'User',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    role: 'doctor',
    clerkId: 'clerk-user-2',
    firstName: 'Dr. <PERSON>',
    lastName: 'Smith',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
]

export const mockTreatments: Treatment[] = [
  {
    id: 'treatment-1',
    name: 'Botox Injection',
    description: 'Anti-aging botox treatment for wrinkles',
    defaultPrice: 300,
    defaultDurationInMinutes: 30,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'treatment-2',
    name: 'Dermal Filler',
    description: 'Hyaluronic acid filler for volume restoration',
    defaultPrice: 500,
    defaultDurationInMinutes: 45,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'treatment-3',
    name: 'Chemical Peel',
    description: 'Exfoliating treatment for skin renewal',
    defaultPrice: 150,
    defaultDurationInMinutes: 60,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
]

export const mockPatients: Patient[] = [
  {
    id: 'patient-1',
    fullName: 'Alice Johnson',
    phone: '******-0101',
    email: '<EMAIL>',
    medicalNotes: 'No known allergies',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'patient-2',
    fullName: 'Bob Wilson',
    phone: '******-0102',
    email: '<EMAIL>',
    medicalNotes: 'Sensitive skin',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'patient-3',
    fullName: 'Carol Davis',
    phone: '******-0103',
    email: '<EMAIL>',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
]

export const mockAppointments: Appointment[] = [
  {
    id: 'appointment-1',
    appointmentDate: '2024-07-10T10:00:00.000Z',
    status: 'scheduled',
    treatment: mockTreatments[0],
    price: 300,
    durationInMinutes: 30,
    patient: mockPatients[0],
    practitioner: mockUsers[1],
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'appointment-2',
    appointmentDate: '2024-07-10T14:00:00.000Z',
    status: 'completed',
    treatment: mockTreatments[1],
    price: 500,
    durationInMinutes: 45,
    patient: mockPatients[1],
    practitioner: mockUsers[1],
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'appointment-3',
    appointmentDate: '2024-07-11T09:00:00.000Z',
    status: 'scheduled',
    treatment: mockTreatments[2],
    price: 150,
    durationInMinutes: 60,
    patient: mockPatients[2],
    practitioner: mockUsers[1],
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
]
