# 账单系统测试指南

## 概述

本文档提供了账单管理系统的全面测试策略，包括单元测试、集成测试和端到端测试的指导。

## 测试环境设置

### 依赖安装
```bash
npm install --save-dev @testing-library/react @testing-library/jest-dom @testing-library/user-event
npm install --save-dev jest jest-environment-jsdom
npm install --save-dev msw  # Mock Service Worker for API mocking
```

### Jest 配置
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/components/billing/**/*.{ts,tsx}',
    'src/lib/api/billing.ts',
    'src/lib/validation/**/*.ts',
    '!src/**/*.d.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

### 测试设置文件
```typescript
// src/test/setup.ts
import '@testing-library/jest-dom';
import { server } from './mocks/server';

// 启动 MSW 服务器
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

// Mock Clerk 认证
jest.mock('@clerk/nextjs', () => ({
  useAuth: () => ({
    isLoaded: true,
    isSignedIn: true,
    userId: 'test-user-id',
  }),
  useUser: () => ({
    isLoaded: true,
    user: {
      id: 'test-user-id',
      firstName: 'Test',
      lastName: 'User',
    },
  }),
}));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
}));
```

## API Mock 设置

### MSW 处理器
```typescript
// src/test/mocks/handlers.ts
import { rest } from 'msw';

export const handlers = [
  // 账单 API
  rest.get('/api/bills', (req, res, ctx) => {
    return res(
      ctx.json({
        docs: [
          {
            id: 'bill-1',
            billNumber: 'BILL-2024-001',
            patient: {
              id: 'patient-1',
              fullName: '张小美',
              phone: '13800138001',
            },
            billType: 'treatment',
            status: 'pending',
            totalAmount: 1500,
            remainingAmount: 1500,
            description: '激光美容治疗',
          },
        ],
        totalDocs: 1,
        page: 1,
        totalPages: 1,
      })
    );
  }),

  rest.post('/api/bills', (req, res, ctx) => {
    return res(
      ctx.json({
        id: 'bill-new',
        billNumber: 'BILL-2024-002',
        status: 'draft',
        // ... 其他字段
      })
    );
  }),

  // 支付 API
  rest.post('/api/payments', (req, res, ctx) => {
    return res(
      ctx.json({
        id: 'payment-1',
        paymentNumber: 'PAY-2024-001',
        amount: 1500,
        paymentStatus: 'completed',
        receiptNumber: 'RCP-2024-001',
      })
    );
  }),

  // 财务报表 API
  rest.get('/api/reports/daily-revenue', (req, res, ctx) => {
    return res(
      ctx.json({
        date: '2024-01-15',
        totalRevenue: 5000,
        paymentCount: 3,
        paymentMethods: {
          cash: { amount: 1000, count: 1 },
          wechat: { amount: 2000, count: 1 },
          card: { amount: 2000, count: 1 },
        },
      })
    );
  }),
];
```

## 单元测试

### 组件测试示例

#### BillForm 组件测试
```typescript
// src/components/billing/__tests__/bill-form.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BillForm } from '../bill-form';

const mockPatients = [
  { id: 'patient-1', fullName: '张小美', phone: '13800138001' },
];

describe('BillForm', () => {
  it('应该渲染账单表单', () => {
    render(
      <BillForm
        patients={mockPatients}
        onSuccess={jest.fn()}
        onCancel={jest.fn()}
      />
    );

    expect(screen.getByText('创建账单')).toBeInTheDocument();
    expect(screen.getByLabelText('患者')).toBeInTheDocument();
    expect(screen.getByLabelText('账单类型')).toBeInTheDocument();
  });

  it('应该验证必填字段', async () => {
    const user = userEvent.setup();
    render(
      <BillForm
        patients={mockPatients}
        onSuccess={jest.fn()}
        onCancel={jest.fn()}
      />
    );

    const submitButton = screen.getByText('创建账单');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('请选择患者')).toBeInTheDocument();
      expect(screen.getByText('账单描述不能为空')).toBeInTheDocument();
    });
  });

  it('应该成功创建账单', async () => {
    const user = userEvent.setup();
    const onSuccess = jest.fn();
    
    render(
      <BillForm
        patients={mockPatients}
        onSuccess={onSuccess}
        onCancel={jest.fn()}
      />
    );

    // 填写表单
    await user.selectOptions(screen.getByLabelText('患者'), 'patient-1');
    await user.type(screen.getByLabelText('账单描述'), '激光美容治疗');
    await user.type(screen.getByLabelText('到期日期'), '2024-02-15');

    // 添加账单项目
    await user.type(screen.getByLabelText('项目名称'), '激光美容');
    await user.type(screen.getByLabelText('数量'), '1');
    await user.type(screen.getByLabelText('单价'), '1500');

    // 提交表单
    await user.click(screen.getByText('创建账单'));

    await waitFor(() => {
      expect(onSuccess).toHaveBeenCalledWith(
        expect.objectContaining({
          billNumber: 'BILL-2024-002',
        })
      );
    });
  });
});
```

#### PaymentForm 组件测试
```typescript
// src/components/billing/__tests__/payment-form.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { PaymentForm } from '../payment-form';

const mockBill = {
  id: 'bill-1',
  billNumber: 'BILL-2024-001',
  totalAmount: 1500,
  remainingAmount: 1500,
  patient: {
    id: 'patient-1',
    fullName: '张小美',
  },
};

describe('PaymentForm', () => {
  it('应该显示账单信息', () => {
    render(
      <PaymentForm
        bill={mockBill}
        onSuccess={jest.fn()}
        onCancel={jest.fn()}
      />
    );

    expect(screen.getByText('张小美')).toBeInTheDocument();
    expect(screen.getByText('¥1,500.00')).toBeInTheDocument();
  });

  it('应该验证支付金额', async () => {
    const user = userEvent.setup();
    render(
      <PaymentForm
        bill={mockBill}
        onSuccess={jest.fn()}
        onCancel={jest.fn()}
      />
    );

    // 输入超过待付金额的数值
    const amountInput = screen.getByLabelText('支付金额');
    await user.clear(amountInput);
    await user.type(amountInput, '2000');

    const submitButton = screen.getByText('确认支付');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/支付金额不能超过待付金额/)).toBeInTheDocument();
    });
  });

  it('应该成功处理支付', async () => {
    const user = userEvent.setup();
    const onSuccess = jest.fn();
    
    render(
      <PaymentForm
        bill={mockBill}
        onSuccess={onSuccess}
        onCancel={jest.fn()}
      />
    );

    // 选择支付方式
    await user.selectOptions(screen.getByLabelText('支付方式'), 'wechat');
    await user.type(screen.getByLabelText('交易ID'), 'wx_123456');

    // 提交支付
    await user.click(screen.getByText('确认支付'));

    await waitFor(() => {
      expect(onSuccess).toHaveBeenCalledWith(
        expect.objectContaining({
          paymentNumber: 'PAY-2024-001',
          amount: 1500,
        })
      );
    });
  });
});
```

### API 客户端测试
```typescript
// src/lib/api/__tests__/billing.test.ts
import { billsAPI, paymentsAPI } from '../billing';

describe('billsAPI', () => {
  it('应该获取账单列表', async () => {
    const bills = await billsAPI.fetchBills();
    
    expect(bills.docs).toHaveLength(1);
    expect(bills.docs[0]).toMatchObject({
      id: 'bill-1',
      billNumber: 'BILL-2024-001',
    });
  });

  it('应该创建新账单', async () => {
    const billData = {
      patient: 'patient-1',
      billType: 'treatment' as const,
      subtotal: 1500,
      totalAmount: 1500,
      description: '激光美容治疗',
      dueDate: '2024-02-15',
      items: [],
    };

    const bill = await billsAPI.createBill(billData);
    
    expect(bill).toMatchObject({
      id: 'bill-new',
      billNumber: 'BILL-2024-002',
    });
  });
});

describe('paymentsAPI', () => {
  it('应该处理支付', async () => {
    const paymentData = {
      bill: 'bill-1',
      patient: 'patient-1',
      amount: 1500,
      paymentMethod: 'wechat' as const,
    };

    const payment = await paymentsAPI.processPayment(paymentData);
    
    expect(payment).toMatchObject({
      id: 'payment-1',
      amount: 1500,
      paymentStatus: 'completed',
    });
  });
});
```

### 验证逻辑测试
```typescript
// src/lib/validation/__tests__/billing-schemas.test.ts
import { validateBillForm, validatePaymentForm } from '../billing-schemas';

describe('账单表单验证', () => {
  it('应该验证有效的账单数据', () => {
    const validBillData = {
      patient: 'patient-1',
      billType: 'treatment',
      description: '激光美容治疗',
      dueDate: '2024-02-15',
      items: [
        {
          itemType: 'treatment',
          itemName: '激光美容',
          quantity: 1,
          unitPrice: 1500,
        },
      ],
    };

    const result = validateBillForm(validBillData);
    expect(result.success).toBe(true);
  });

  it('应该拒绝无效的账单数据', () => {
    const invalidBillData = {
      patient: '',
      billType: 'invalid',
      description: '',
      items: [],
    };

    const result = validateBillForm(invalidBillData);
    expect(result.success).toBe(false);
    expect(result.error?.errors).toContainEqual(
      expect.objectContaining({
        path: ['patient'],
        message: '请选择患者',
      })
    );
  });
});
```

## 集成测试

### 完整用户流程测试
```typescript
// src/test/integration/billing-workflow.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BillingTabs } from '@/components/billing/billing-tabs';

describe('账单管理工作流程', () => {
  it('应该完成完整的账单创建和支付流程', async () => {
    const user = userEvent.setup();
    
    render(<BillingTabs defaultTab="bills" />);

    // 1. 创建新账单
    await user.click(screen.getByText('新建账单'));
    
    // 填写账单信息
    await user.selectOptions(screen.getByLabelText('患者'), 'patient-1');
    await user.type(screen.getByLabelText('账单描述'), '激光美容治疗');
    await user.type(screen.getByLabelText('到期日期'), '2024-02-15');
    
    // 添加项目
    await user.type(screen.getByLabelText('项目名称'), '激光美容');
    await user.type(screen.getByLabelText('单价'), '1500');
    
    // 保存账单
    await user.click(screen.getByText('创建账单'));
    
    await waitFor(() => {
      expect(screen.getByText('账单创建成功')).toBeInTheDocument();
    });

    // 2. 处理支付
    await user.click(screen.getByText('收款'));
    
    // 选择支付方式
    await user.selectOptions(screen.getByLabelText('支付方式'), 'wechat');
    await user.type(screen.getByLabelText('交易ID'), 'wx_123456');
    
    // 确认支付
    await user.click(screen.getByText('确认支付'));
    
    await waitFor(() => {
      expect(screen.getByText('支付处理成功')).toBeInTheDocument();
    });

    // 3. 验证账单状态更新
    expect(screen.getByText('已支付')).toBeInTheDocument();
  });
});
```

## 端到端测试 (E2E)

### Playwright 测试示例
```typescript
// e2e/billing.spec.ts
import { test, expect } from '@playwright/test';

test.describe('账单管理系统', () => {
  test.beforeEach(async ({ page }) => {
    // 登录并导航到账单页面
    await page.goto('/dashboard/billing');
    await page.waitForLoadState('networkidle');
  });

  test('应该创建新账单', async ({ page }) => {
    // 点击新建账单按钮
    await page.click('text=新建账单');
    
    // 填写表单
    await page.selectOption('[data-testid=patient-select]', 'patient-1');
    await page.fill('[data-testid=description-input]', '激光美容治疗');
    await page.fill('[data-testid=due-date-input]', '2024-02-15');
    
    // 添加账单项目
    await page.fill('[data-testid=item-name-input]', '激光美容');
    await page.fill('[data-testid=unit-price-input]', '1500');
    
    // 提交表单
    await page.click('text=创建账单');
    
    // 验证成功消息
    await expect(page.locator('text=账单创建成功')).toBeVisible();
  });

  test('应该处理支付', async ({ page }) => {
    // 找到待支付的账单
    await page.click('[data-testid=bill-payment-button]');
    
    // 选择支付方式
    await page.selectOption('[data-testid=payment-method-select]', 'wechat');
    await page.fill('[data-testid=transaction-id-input]', 'wx_123456');
    
    // 确认支付
    await page.click('text=确认支付');
    
    // 验证支付成功
    await expect(page.locator('text=支付处理成功')).toBeVisible();
    
    // 验证收据显示
    await expect(page.locator('[data-testid=receipt-dialog]')).toBeVisible();
  });

  test('应该生成财务报表', async ({ page }) => {
    // 切换到报表标签
    await page.click('text=财务报表');
    
    // 等待数据加载
    await page.waitForSelector('[data-testid=daily-revenue-chart]');
    
    // 验证报表内容
    await expect(page.locator('text=今日收入')).toBeVisible();
    await expect(page.locator('text=本月收入')).toBeVisible();
    await expect(page.locator('text=应收账款')).toBeVisible();
  });
});
```

## 性能测试

### 组件渲染性能测试
```typescript
// src/test/performance/billing-performance.test.tsx
import { render } from '@testing-library/react';
import { BillingList } from '@/components/billing/billing-list';

describe('账单列表性能测试', () => {
  it('应该在合理时间内渲染大量账单', () => {
    const startTime = performance.now();
    
    render(<BillingList />);
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    // 渲染时间应该少于100ms
    expect(renderTime).toBeLessThan(100);
  });
});
```

## 测试运行命令

### 单元测试
```bash
# 运行所有测试
npm test

# 运行特定测试文件
npm test billing-form.test.tsx

# 运行测试并生成覆盖率报告
npm test -- --coverage

# 监视模式运行测试
npm test -- --watch
```

### E2E 测试
```bash
# 安装 Playwright
npx playwright install

# 运行 E2E 测试
npx playwright test

# 运行特定测试
npx playwright test billing.spec.ts

# 调试模式运行
npx playwright test --debug
```

## 测试最佳实践

### 1. 测试命名规范
- 使用描述性的测试名称
- 使用中文描述用户行为
- 遵循 "应该 + 期望行为" 的格式

### 2. 测试数据管理
- 使用工厂函数创建测试数据
- 避免硬编码测试数据
- 每个测试使用独立的数据

### 3. 异步测试处理
- 使用 `waitFor` 等待异步操作
- 避免使用 `setTimeout`
- 正确处理 Promise 和 async/await

### 4. Mock 策略
- Mock 外部依赖 (API、第三方库)
- 保持 Mock 简单和可维护
- 使用 MSW 进行 API Mock

### 5. 测试覆盖率目标
- 代码覆盖率 > 80%
- 关键业务逻辑 100% 覆盖
- 定期审查覆盖率报告

## 持续集成

### GitHub Actions 配置
```yaml
# .github/workflows/test.yml
name: 测试

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: 安装依赖
        run: npm ci
        
      - name: 运行单元测试
        run: npm test -- --coverage
        
      - name: 运行 E2E 测试
        run: npx playwright test
        
      - name: 上传覆盖率报告
        uses: codecov/codecov-action@v3
```

通过遵循这个测试指南，可以确保账单管理系统的质量和可靠性。
