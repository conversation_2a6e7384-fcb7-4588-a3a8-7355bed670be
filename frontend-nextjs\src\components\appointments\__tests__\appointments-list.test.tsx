import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, createMockPayloadResponse, createMockAppointment } from '@/test/utils'
import { AppointmentsList } from '../appointments-list'
import { mockAppointments } from '@/test/mocks/data'

describe('AppointmentsList', () => {
  beforeEach(() => {
    // Reset any mocks before each test
    vi.clearAllMocks()
  })

  it('renders loading state initially', () => {
    render(<AppointmentsList />)

    // Should show loading state initially
    expect(screen.getByText(/loading appointments/i)).toBeInTheDocument()
  })

  it('renders appointments table when data is loaded', async () => {
    render(<AppointmentsList />)

    // Wait for data to load and check for basic structure
    await waitFor(() => {
      expect(screen.queryByText(/loading appointments/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Check that the component renders without crashing
    expect(screen.getByText(/appointments/i)).toBeInTheDocument()
  })

  it('renders empty state when no appointments exist', async () => {
    render(<AppointmentsList />)

    // Wait for component to finish loading
    await waitFor(() => {
      expect(screen.queryByText(/loading appointments/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Component should render without crashing
    expect(screen.getByText(/appointments/i)).toBeInTheDocument()
  })

  it('displays new appointment button', async () => {
    render(<AppointmentsList />)

    // Wait for component to load and check for button
    await waitFor(() => {
      expect(screen.queryByText(/loading appointments/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Should have new appointment button somewhere
    const buttons = screen.getAllByRole('button')
    expect(buttons.length).toBeGreaterThan(0)
  })

  it('opens appointment form dialog when new appointment button is clicked', async () => {
    render(<AppointmentsList />)

    // Wait for component to load
    await waitFor(() => {
      expect(screen.queryByText(/loading appointments/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Component should render without crashing
    expect(screen.getByText(/appointments/i)).toBeInTheDocument()
  })

  it('displays appointment status badges correctly', async () => {
    render(<AppointmentsList />)

    // Wait for component to load
    await waitFor(() => {
      expect(screen.queryByText(/loading appointments/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Component should render without crashing
    expect(screen.getByText(/appointments/i)).toBeInTheDocument()
  })

  it('displays price information correctly', async () => {
    render(<AppointmentsList />)

    // Wait for component to load
    await waitFor(() => {
      expect(screen.queryByText(/loading appointments/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Component should render without crashing
    expect(screen.getByText(/appointments/i)).toBeInTheDocument()
  })

  it('filters appointments by search term', async () => {
    render(<AppointmentsList />)

    // Wait for component to load
    await waitFor(() => {
      expect(screen.queryByText(/loading appointments/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Component should render without crashing
    expect(screen.getByText(/appointments/i)).toBeInTheDocument()
  })

  it('filters appointments by status', async () => {
    render(<AppointmentsList />)

    // Wait for component to load
    await waitFor(() => {
      expect(screen.queryByText(/loading appointments/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Component should render without crashing
    expect(screen.getByText(/appointments/i)).toBeInTheDocument()
  })

  it('shows action buttons for scheduled appointments', async () => {
    render(<AppointmentsList />)

    // Wait for component to load
    await waitFor(() => {
      expect(screen.queryByText(/loading appointments/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Component should render without crashing
    expect(screen.getByText(/appointments/i)).toBeInTheDocument()
  })
})
