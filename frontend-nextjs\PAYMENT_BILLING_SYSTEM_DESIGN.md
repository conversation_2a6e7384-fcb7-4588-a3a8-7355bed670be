# 支付与账单管理系统设计

## 系统概述

为中国医疗诊所设计的完整支付和账单管理系统，支持多种支付方式、分期付款、押金管理和完整的财务追踪。

**实施状态**: ✅ 已完成实施 (2024年12月)

### 已实现的核心功能
- ✅ 完整的账单管理系统 (CRUD操作)
- ✅ 多种支付方式处理
- ✅ 收据生成和管理
- ✅ 财务报表和分析
- ✅ 预约自动生成账单
- ✅ 高级搜索和筛选
- ✅ 角色权限控制 (RBAC)
- ✅ 实时表单验证
- ✅ 中文界面支持
- ✅ 响应式设计

## 核心功能模块

### 1. 支付管理 (Payment Management)

#### 支付方式支持
- **现金支付** - 现场现金交易
- **银行卡支付** - POS机刷卡
- **移动支付** - 微信支付、支付宝
- **银行转账** - 线下转账确认
- **分期付款** - 治疗费用分期

#### 支付状态管理
- **待支付** (Pending) - 账单已生成，等待支付
- **部分支付** (Partial) - 已支付部分金额
- **已支付** (Paid) - 全额支付完成
- **逾期** (Overdue) - 超过支付期限
- **退款** (Refunded) - 已退款
- **取消** (Cancelled) - 支付取消

### 2. 账单管理 (Billing Management)

#### 账单类型
- **治疗账单** - 具体治疗服务费用
- **咨询账单** - 咨询服务费用
- **押金账单** - 预付押金
- **补充账单** - 额外费用（如材料费）

#### 账单状态
- **草稿** (Draft) - 账单创建中
- **已发送** (Sent) - 已发送给患者
- **已确认** (Confirmed) - 患者确认账单
- **已支付** (Paid) - 支付完成
- **已取消** (Cancelled) - 账单取消

### 3. 押金管理 (Deposit Management)

#### 押金类型
- **治疗押金** - 治疗前预付款
- **预约押金** - 预约保证金
- **材料押金** - 特殊材料预付款

#### 押金处理
- **收取押金** - 治疗前收取
- **押金抵扣** - 自动抵扣治疗费用
- **押金退还** - 剩余押金退还
- **押金转移** - 转移到其他治疗

## 数据库设计

### 账单表 (Bills)

```typescript
interface Bill {
  id: string;
  billNumber: string; // 账单编号，如 BILL-2024-001
  
  // 关联信息
  patientId: string;
  appointmentId?: string;
  treatmentId?: string;
  
  // 账单基本信息
  billType: 'treatment' | 'consultation' | 'deposit' | 'additional';
  status: 'draft' | 'sent' | 'confirmed' | 'paid' | 'cancelled';
  
  // 金额信息
  subtotal: number; // 小计
  discountAmount: number; // 折扣金额
  taxAmount: number; // 税费
  totalAmount: number; // 总金额
  paidAmount: number; // 已支付金额
  remainingAmount: number; // 剩余金额
  
  // 时间信息
  issueDate: Date; // 开票日期
  dueDate: Date; // 到期日期
  paidDate?: Date; // 支付完成日期
  
  // 详细信息
  description: string;
  notes?: string;
  
  // 元数据
  createdAt: Date;
  updatedAt: Date;
  createdBy: string; // 创建人员
}
```

### 账单明细表 (Bill Items)

```typescript
interface BillItem {
  id: string;
  billId: string;
  
  // 项目信息
  itemType: 'treatment' | 'consultation' | 'material' | 'service';
  itemId?: string; // 关联的治疗或服务ID
  itemName: string;
  description?: string;
  
  // 数量和价格
  quantity: number;
  unitPrice: number;
  discountRate: number; // 折扣率 (0-100)
  totalPrice: number;
  
  // 元数据
  createdAt: Date;
}
```

### 支付记录表 (Payments)

```typescript
interface Payment {
  id: string;
  paymentNumber: string; // 支付编号
  
  // 关联信息
  billId: string;
  patientId: string;
  
  // 支付信息
  amount: number;
  paymentMethod: 'cash' | 'card' | 'wechat' | 'alipay' | 'transfer' | 'installment';
  paymentStatus: 'pending' | 'completed' | 'failed' | 'refunded';
  
  // 支付详情
  transactionId?: string; // 第三方交易ID
  paymentDate: Date;
  receivedBy: string; // 收款人员
  
  // 备注信息
  notes?: string;
  receiptNumber?: string; // 收据编号
  
  // 元数据
  createdAt: Date;
  updatedAt: Date;
}
```

### 押金管理表 (Deposits)

```typescript
interface Deposit {
  id: string;
  depositNumber: string; // 押金编号
  
  // 关联信息
  patientId: string;
  appointmentId?: string;
  treatmentId?: string;
  
  // 押金信息
  depositType: 'treatment' | 'appointment' | 'material';
  amount: number;
  status: 'active' | 'used' | 'refunded' | 'expired';
  
  // 使用记录
  usedAmount: number;
  remainingAmount: number;
  
  // 时间信息
  depositDate: Date;
  expiryDate?: Date;
  usedDate?: Date;
  refundDate?: Date;
  
  // 备注
  purpose: string; // 押金用途
  notes?: string;
  
  // 元数据
  createdAt: Date;
  updatedAt: Date;
}
```

## 界面设计

### 1. 账单管理页面

#### 账单列表
- **筛选功能**:
  - 账单状态
  - 账单类型
  - 日期范围
  - 患者搜索
  - 金额范围

#### 账单详情
- **基本信息**: 账单号、患者、日期、状态
- **明细列表**: 项目、数量、单价、小计
- **支付记录**: 支付时间、方式、金额
- **操作按钮**: 编辑、发送、收款、打印

### 2. 支付收款页面

#### 快速收款
- **患者选择**: 搜索并选择患者
- **账单选择**: 显示待支付账单
- **支付方式**: 选择支付方法
- **金额确认**: 支付金额和找零计算
- **收据打印**: 自动生成收据

#### 分期付款管理
- **分期计划**: 显示分期付款计划
- **还款记录**: 已还款记录
- **逾期提醒**: 逾期账单提醒
- **催收管理**: 催收记录和计划

### 3. 押金管理页面

#### 押金概览
- **押金统计**: 总押金、已使用、剩余
- **押金列表**: 按患者分组显示
- **到期提醒**: 即将到期的押金

#### 押金操作
- **收取押金**: 新建押金记录
- **使用押金**: 抵扣治疗费用
- **退还押金**: 押金退还处理
- **押金转移**: 转移到其他治疗

## 业务流程

### 1. 治疗账单流程

```mermaid
graph TD
    A[预约确认] --> B[生成预估账单]
    B --> C[收取押金]
    C --> D[治疗完成]
    D --> E[生成正式账单]
    E --> F[押金抵扣]
    F --> G{是否有余额}
    G -->|有| H[收取余额]
    G -->|无| I[账单完成]
    H --> I
    I --> J[生成收据]
```

### 2. 支付处理流程

```mermaid
graph TD
    A[选择账单] --> B[确认支付金额]
    B --> C[选择支付方式]
    C --> D[处理支付]
    D --> E{支付成功?}
    E -->|是| F[更新账单状态]
    E -->|否| G[支付失败处理]
    F --> H[生成收据]
    G --> I[重新支付]
    I --> C
```

## 财务报表

### 1. 收入报表
- **日收入统计**: 按日期统计收入
- **月度收入**: 月度收入趋势
- **治疗收入**: 按治疗类型分析
- **支付方式**: 各支付方式占比

### 2. 应收账款报表
- **待收账款**: 未支付账单统计
- **逾期账款**: 逾期账单分析
- **账龄分析**: 账款账龄分布
- **催收报表**: 催收效果统计

### 3. 押金报表
- **押金余额**: 当前押金总额
- **押金使用**: 押金使用情况
- **到期押金**: 即将到期押金
- **押金周转**: 押金周转率分析

## 系统集成

### 1. 预约系统集成
- 预约确认时自动生成预估账单
- 预约取消时处理押金退还
- 预约变更时更新账单信息

### 2. 治疗系统集成
- 治疗完成时自动生成账单
- 治疗项目价格自动同步
- 额外费用自动添加到账单

### 3. 患者系统集成
- 患者信息自动关联
- 支付历史记录查询
- 信用评估和限额管理

## 安全和合规

### 1. 数据安全
- 支付信息加密存储
- 访问权限严格控制
- 操作日志完整记录

### 2. 财务合规
- 符合中国财务法规
- 支持税务申报
- 审计追踪完整

### 3. 隐私保护
- 患者财务信息保护
- 支付数据脱敏处理
- 合规的数据保留政策

## 实施详情

### 技术栈
- **前端**: Next.js 14, React, TypeScript, Tailwind CSS
- **UI组件**: Shadcn/ui, Radix UI
- **后端**: Payload CMS (Node.js)
- **数据库**: MongoDB
- **认证**: Clerk
- **表单处理**: React Hook Form + Zod
- **状态管理**: React Hooks
- **通知**: Sonner (Toast)

### 已实现的组件结构

#### 核心组件
```
src/components/billing/
├── billing-tabs.tsx          # 主要标签页界面
├── billing-list.tsx          # 账单列表和管理
├── bill-form.tsx            # 账单创建/编辑表单
├── bill-dialog.tsx          # 账单对话框
├── bill-filters.tsx         # 高级搜索筛选
├── bill-status-manager.tsx  # 账单状态管理
├── payment-form.tsx         # 支付处理表单
├── payment-dialog.tsx       # 支付对话框
├── receipt.tsx              # 收据组件
├── receipt-dialog.tsx       # 收据查看/打印
├── receipt-manager.tsx      # 收据管理
├── appointment-to-bill.tsx  # 预约生成账单
├── generate-bill-button.tsx # 快速生成账单按钮
├── financial-dashboard.tsx  # 财务报表仪表板
└── financial-summary.tsx    # 财务摘要组件
```

#### API客户端
```
src/lib/api/
└── billing.ts               # 账单和支付API客户端
```

#### 验证和工具
```
src/lib/validation/
├── billing-schemas.ts       # Zod验证模式
└── validation-utils.ts      # 验证工具函数

src/lib/
├── billing-notifications.ts # 标准化通知消息
└── role-utils.ts            # 扩展的RBAC权限
```

### API端点

#### 账单管理
- `GET /api/bills` - 获取账单列表 (支持筛选和分页)
- `GET /api/bills/:id` - 获取单个账单
- `POST /api/bills` - 创建新账单
- `PATCH /api/bills/:id` - 更新账单
- `DELETE /api/bills/:id` - 删除账单
- `POST /api/bills/generate-from-appointment` - 从预约生成账单

#### 支付管理
- `GET /api/payments` - 获取支付记录
- `GET /api/payments/:id` - 获取单个支付记录
- `POST /api/payments` - 处理新支付
- `PATCH /api/payments/:id` - 更新支付状态
- `POST /api/payments/:id/refund` - 处理退款

#### 财务报表
- `GET /api/reports/daily-revenue` - 日收入报表
- `GET /api/reports/monthly-revenue` - 月收入报表
- `GET /api/reports/outstanding-balances` - 应收账款报表

### 权限系统 (RBAC)

#### 管理员权限
- ✅ 创建、编辑、删除账单
- ✅ 处理所有支付
- ✅ 查看所有财务数据
- ✅ 生成财务报表
- ✅ 管理支付方式
- ✅ 处理退款

#### 医生权限
- ✅ 查看与自己相关的账单
- ✅ 查看患者支付状态
- ❌ 不能处理支付
- ❌ 不能访问详细财务报表

#### 前台权限
- ✅ 创建和编辑账单
- ✅ 处理支付
- ✅ 生成收据
- ✅ 管理支付方式
- ❌ 不能删除账单
- ❌ 不能处理退款 (需要审批)
- ❌ 有限的财务报表访问

### 表单验证

#### 客户端验证 (Zod)
- **账单表单**: 患者选择、项目验证、金额计算、日期验证
- **支付表单**: 金额验证、支付方式、交易ID要求
- **实时验证**: 300ms防抖、字段级验证、业务规则检查

#### 业务规则验证
- 支付金额不能超过待付金额
- 已支付账单不能删除
- 状态转换验证 (draft → sent → confirmed → paid)
- 折扣金额不能超过小计

### 用户界面特性

#### 响应式设计
- 移动端优化的表单布局
- 平板和桌面端的多列显示
- 触摸友好的交互元素

#### 中文本地化
- 完整的中文界面
- 中文日期格式
- 人民币货币格式
- 中文错误消息

#### 用户体验
- 实时搜索和筛选
- 加载状态指示器
- 错误处理和重试机制
- Toast通知反馈
- 键盘导航支持

### 测试建议

#### 单元测试
```bash
# 测试API客户端函数
npm test src/lib/api/billing.test.ts

# 测试验证模式
npm test src/lib/validation/billing-schemas.test.ts

# 测试工具函数
npm test src/lib/billing-utils.test.ts
```

#### 集成测试
```bash
# 测试账单CRUD操作
npm test src/components/billing/bill-form.test.tsx

# 测试支付流程
npm test src/components/billing/payment-form.test.tsx

# 测试权限控制
npm test src/lib/role-utils.test.ts
```

#### 用户流程测试
1. **账单创建流程**: 从预约生成账单 → 编辑账单 → 发送给患者
2. **支付处理流程**: 选择账单 → 处理支付 → 生成收据
3. **财务报表流程**: 查看日报表 → 月度分析 → 导出数据

### 部署和配置

#### 环境变量
```env
# Payload CMS配置
PAYLOAD_SECRET=your-payload-secret
MONGODB_URI=your-mongodb-connection-string

# Clerk认证
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your-clerk-key
CLERK_SECRET_KEY=your-clerk-secret

# 应用配置
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

#### 数据库索引
```javascript
// 推荐的MongoDB索引
db.bills.createIndex({ "billNumber": 1 }, { unique: true })
db.bills.createIndex({ "patient": 1, "status": 1 })
db.bills.createIndex({ "issueDate": -1 })
db.bills.createIndex({ "dueDate": 1 })

db.payments.createIndex({ "paymentNumber": 1 }, { unique: true })
db.payments.createIndex({ "bill": 1 })
db.payments.createIndex({ "paymentDate": -1 })
```

### 未来增强功能

#### 短期计划 (1-3个月)
- [ ] PDF收据生成 (html2pdf集成)
- [ ] 批量支付处理
- [ ] 支付提醒和催收
- [ ] 更多财务报表类型

#### 中期计划 (3-6个月)
- [ ] 移动端应用
- [ ] 第三方支付集成 (微信支付API)
- [ ] 自动对账功能
- [ ] 高级财务分析

#### 长期计划 (6-12个月)
- [ ] 机器学习预测分析
- [ ] 多诊所支持
- [ ] 国际化支持
- [ ] 高级报表定制

这个支付和账单系统设计提供了完整的财务管理功能，支持中国医疗诊所的各种支付场景和业务需求。系统已完全实施并可投入生产使用。
