'use client';

import { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useRole, PermissionGate } from '@/lib/role-context';
import {
  IconPlus,
  IconReceipt,
  IconSearch,
  IconEdit,
  IconEye,
  IconCreditCard,
  IconCash,
  IconAlertCircle,
  IconRefresh
} from '@tabler/icons-react';
import { Bill, Payment } from '@/types/clinic';
import { toast } from 'sonner';
import { t } from '@/lib/translations';
import { billsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';
import { PaymentDialog } from './payment-dialog';
import { BillDialog } from './bill-dialog';
import { BillStatusManager } from './bill-status-manager';
import { BillFilters, BillFilterOptions } from './bill-filters';

// Status mapping for bill status
const getStatusVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (status) {
    case 'paid':
      return 'default';
    case 'confirmed':
      return 'secondary';
    case 'cancelled':
      return 'destructive';
    default:
      return 'outline';
  }
};

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'paid':
      return 'text-green-600 bg-green-50 border-green-200';
    case 'confirmed':
      return 'text-blue-600 bg-blue-50 border-blue-200';
    case 'cancelled':
      return 'text-red-600 bg-red-50 border-red-200';
    case 'draft':
      return 'text-gray-600 bg-gray-50 border-gray-200';
    case 'sent':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
};

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case 'partial':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'overdue':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
        return '已支付';
      case 'pending':
        return '待支付';
      case 'partial':
        return '部分支付';
      case 'overdue':
        return '逾期';
      case 'cancelled':
        return '已取消';
      default:
        return status;
    }
  };

  return (
    <Badge className={getStatusColor(status)}>
      {getStatusText(status)}
    </Badge>
  );
};

// Bill type badge component
const BillTypeBadge = ({ type }: { type: string }) => {
  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'treatment':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      case 'consultation':
        return 'bg-orange-100 text-orange-800 hover:bg-orange-200';
      case 'deposit':
        return 'bg-cyan-100 text-cyan-800 hover:bg-cyan-200';
      case 'additional':
        return 'bg-pink-100 text-pink-800 hover:bg-pink-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const getTypeText = (type: string) => {
    switch (type.toLowerCase()) {
      case 'treatment':
        return '治疗';
      case 'consultation':
        return '咨询';
      case 'deposit':
        return '押金';
      case 'additional':
        return '附加';
      default:
        return type;
    }
  };

  return (
    <Badge variant="outline" className={getTypeColor(type)}>
      {getTypeText(type)}
    </Badge>
  );
};

export function BillingList() {
  const { hasPermission } = useRole();
  const [bills, setBills] = useState<Bill[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<BillFilterOptions>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [refreshing, setRefreshing] = useState(false);
  const [patients, setPatients] = useState<Array<{ id: string; fullName: string; phone: string }>>([]);
  const [selectedBillForPayment, setSelectedBillForPayment] = useState<Bill | null>(null);
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [selectedBillForEdit, setSelectedBillForEdit] = useState<Bill | null>(null);
  const [isBillDialogOpen, setIsBillDialogOpen] = useState(false);
  const [isCreatingBill, setIsCreatingBill] = useState(false);

  // Fetch bills from API
  const fetchBills = async (page: number = 1, currentFilters: BillFilterOptions = {}) => {
    try {
      setLoading(page === 1);
      setError(null);

      const response = await billsAPI.fetchBills({
        page,
        limit: 10,
        search: currentFilters.search || undefined,
        status: currentFilters.status || undefined,
        patientId: currentFilters.patientId || undefined,
        dateFrom: currentFilters.dateFrom || undefined,
        dateTo: currentFilters.dateTo || undefined,
      });

      setBills(response.docs);
      setCurrentPage(response.page);
      setTotalPages(response.totalPages);
    } catch (err) {
      console.error('Failed to fetch bills:', err);
      const errorMessage = err instanceof BillingAPIError
        ? err.message
        : '加载账单失败，请稍后重试。';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fetch patients for filter dropdown
  const fetchPatients = async () => {
    try {
      const response = await fetch('/api/patients');
      const data = await response.json();
      setPatients(data.docs || []);
    } catch (error) {
      console.error('Failed to fetch patients:', error);
    }
  };

  // Initial load
  useEffect(() => {
    fetchBills(1, filters);
    fetchPatients();
  }, []);

  // Handle filter changes with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchBills(1, filters);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [filters]);

  // Refresh bills
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchBills(currentPage, filters);
  };

  const handleFiltersChange = (newFilters: BillFilterOptions) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleNewBill = () => {
    setSelectedBillForEdit(null);
    setIsCreatingBill(true);
    setIsBillDialogOpen(true);
  };

  const handleViewBill = (bill: Bill) => {
    toast.info(`查看账单: ${bill.billNumber}`);
    // TODO: Implement bill view dialog
  };

  const handleEditBill = (bill: Bill) => {
    setSelectedBillForEdit(bill);
    setIsCreatingBill(false);
    setIsBillDialogOpen(true);
  };

  const handlePayment = (bill: Bill) => {
    setSelectedBillForPayment(bill);
    setIsPaymentDialogOpen(true);
  };

  const handlePaymentSuccess = (payment: Payment) => {
    toast.success(`支付处理成功！收据编号: ${payment.receiptNumber || '待生成'}`);
    // Refresh the bills list to show updated payment status
    fetchBills(currentPage, filters);
  };

  const handleClosePaymentDialog = () => {
    setIsPaymentDialogOpen(false);
    setSelectedBillForPayment(null);
  };

  const handleBillSuccess = (bill: Bill) => {
    const action = isCreatingBill ? '创建' : '更新';
    toast.success(`账单${action}成功！账单编号: ${bill.billNumber}`);
    // Refresh the bills list
    fetchBills(currentPage, filters);
  };

  const handleCloseBillDialog = () => {
    setIsBillDialogOpen(false);
    setSelectedBillForEdit(null);
    setIsCreatingBill(false);
  };

  const handleStatusUpdate = (updatedBill: Bill) => {
    // Update the bill in the local state
    setBills(prevBills =>
      prevBills.map(bill =>
        bill.id === updatedBill.id ? updatedBill : bill
      )
    );
    toast.success('账单状态已更新');
  };

  const handlePageChange = (page: number) => {
    fetchBills(page, filters);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载账单中...</p>
        </div>
      </div>
    );
  }

  if (error && bills.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <IconAlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-600 mb-2">加载失败</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={handleRefresh} variant="outline">
            <IconRefresh className="h-4 w-4 mr-2" />
            重试
          </Button>
        </div>
      </div>
    );
  }

  if (bills.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <IconReceipt className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">暂无账单</h3>
          <p className="text-muted-foreground mb-4">
            开始创建您的第一个账单。
          </p>
          <PermissionGate permission="canCreateBills">
            <Button onClick={handleNewBill}>
              <IconPlus className="h-4 w-4 mr-2" />
              新建账单
            </Button>
          </PermissionGate>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Advanced Filters */}
      <BillFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        patients={patients}
      />

      {/* Header with actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <IconRefresh className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <div className="text-sm text-muted-foreground">
            共 {bills.length} 个账单
          </div>
        </div>
        <PermissionGate permission="canCreateBills">
          <Button onClick={handleNewBill}>
            <IconPlus className="h-4 w-4 mr-2" />
            新建账单
          </Button>
        </PermissionGate>
      </div>

      {/* Error banner for non-fatal errors */}
      {error && bills.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <div className="flex items-center">
            <IconAlertCircle className="h-4 w-4 text-red-500 mr-2" />
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Bills Grid */}
      <div className="grid gap-4">
        {bills.map((bill) => (
          <div key={bill.id} className="border rounded-lg p-4 space-y-3">
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <h4 className="font-medium text-lg">{bill.billNumber}</h4>
                  <BillTypeBadge type={bill.billType} />
                  <BillStatusManager
                    bill={bill}
                    onStatusUpdate={handleStatusUpdate}
                    trigger={<StatusBadge status={bill.status} />}
                  />
                </div>
                <div className="text-sm text-muted-foreground">
                  <p>患者: {typeof bill.patient === 'object' ? bill.patient.fullName : '未知患者'}</p>
                  <p>描述: {bill.description}</p>
                  <p>开票日期: {new Date(bill.issueDate).toLocaleDateString('zh-CN')}</p>
                </div>
              </div>
              
              <div className="text-right space-y-1">
                <div className="text-lg font-semibold">
                  {billingUtils.formatCurrency(bill.totalAmount)}
                </div>
                {(bill.remainingAmount || 0) > 0 && (
                  <div className="text-sm text-red-600">
                    待收: {billingUtils.formatCurrency(bill.remainingAmount || 0)}
                  </div>
                )}
                {(bill.paidAmount || 0) > 0 && (
                  <div className="text-sm text-green-600">
                    已收: {billingUtils.formatCurrency(bill.paidAmount || 0)}
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex items-center justify-between pt-2 border-t">
              <div className="text-xs text-muted-foreground">
                到期日期: {new Date(bill.dueDate).toLocaleDateString('zh-CN')}
              </div>
              
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" onClick={() => handleViewBill(bill)}>
                  <IconEye className="h-4 w-4" />
                </Button>
                <PermissionGate permission="canEditBills">
                  <Button variant="ghost" size="sm" onClick={() => handleEditBill(bill)}>
                    <IconEdit className="h-4 w-4" />
                  </Button>
                </PermissionGate>
                {(bill.remainingAmount || 0) > 0 && (
                  <PermissionGate permission="canProcessPayments">
                    <Button variant="default" size="sm" onClick={() => handlePayment(bill)}>
                      <IconCreditCard className="h-4 w-4 mr-1" />
                      收款
                    </Button>
                  </PermissionGate>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2 pt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage <= 1}
          >
            上一页
          </Button>
          <span className="text-sm text-muted-foreground">
            第 {currentPage} 页，共 {totalPages} 页
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
          >
            下一页
          </Button>
        </div>
      )}

      {/* Payment Dialog */}
      <PaymentDialog
        bill={selectedBillForPayment}
        isOpen={isPaymentDialogOpen}
        onClose={handleClosePaymentDialog}
        onSuccess={handlePaymentSuccess}
      />

      {/* Bill Creation/Edit Dialog */}
      <BillDialog
        bill={selectedBillForEdit}
        isOpen={isBillDialogOpen}
        onClose={handleCloseBillDialog}
        onSuccess={handleBillSuccess}
      />
    </div>
  );
}
