import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../lib/payload-auth-middleware';

/**
 * GET /api/deposits - Get all deposits
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Extract query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const page = parseInt(url.searchParams.get('page') || '1');
    const depth = parseInt(url.searchParams.get('depth') || '2');
    const patientId = url.searchParams.get('patientId');
    const depositType = url.searchParams.get('depositType');
    const status = url.searchParams.get('status');

    // Build where clause for filtering
    const where: Record<string, unknown> = {};
    if (patientId) {
      where.patient = { equals: patientId };
    }
    if (depositType) {
      where.depositType = { equals: depositType };
    }
    if (status) {
      where.status = { equals: status };
    }

    // Fetch deposits from Payload CMS with relationships
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'deposits',
      'find',
      {
        limit,
        page,
        depth, // Include patient, appointment, and treatment relationships
        where: Object.keys(where).length > 0 ? where : undefined,
        sort: '-depositDate', // Sort by newest first
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching deposits:', error);
    return NextResponse.json(
      { error: 'Failed to fetch deposits' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/deposits - Create a new deposit
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const depositData = await request.json();

    // Validate required fields
    if (!depositData.patient) {
      return NextResponse.json(
        { error: 'Patient is required' },
        { status: 400 }
      );
    }

    if (!depositData.amount || depositData.amount <= 0) {
      return NextResponse.json(
        { error: 'Valid deposit amount is required' },
        { status: 400 }
      );
    }

    if (!depositData.purpose) {
      return NextResponse.json(
        { error: 'Deposit purpose is required' },
        { status: 400 }
      );
    }

    // Create deposit in Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'deposits',
      'create',
      {
        data: depositData,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error creating deposit:', error);
    return NextResponse.json(
      { error: 'Failed to create deposit' },
      { status: 500 }
    );
  }
}
