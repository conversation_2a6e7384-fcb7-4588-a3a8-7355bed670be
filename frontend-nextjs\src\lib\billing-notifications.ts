// Comprehensive toast notification utilities for billing actions
// Provides consistent messaging in Chinese for all billing operations

import { toast } from 'sonner';
import { Bill, Payment, BillItem } from '@/types/clinic';
import { billingUtils } from './api/billing';

export const billingNotifications = {
  // Bill-related notifications
  bill: {
    created: (bill: Bill) => {
      toast.success(`账单创建成功！`, {
        description: `账单编号: ${bill.billNumber}`,
        duration: 4000,
      });
    },

    updated: (bill: Bill) => {
      toast.success(`账单更新成功！`, {
        description: `账单编号: ${bill.billNumber}`,
        duration: 4000,
      });
    },

    deleted: (billNumber: string) => {
      toast.success(`账单删除成功！`, {
        description: `账单编号: ${billNumber}`,
        duration: 4000,
      });
    },

    statusUpdated: (bill: Bill, oldStatus: string, newStatus: string) => {
      const statusNames = {
        draft: '草稿',
        sent: '已发送',
        confirmed: '已确认',
        paid: '已支付',
        cancelled: '已取消',
      };
      
      toast.success(`账单状态已更新！`, {
        description: `${bill.billNumber}: ${statusNames[oldStatus as keyof typeof statusNames]} → ${statusNames[newStatus as keyof typeof statusNames]}`,
        duration: 5000,
      });
    },

    generateFromAppointment: (bill: Bill, appointmentDate: string) => {
      toast.success(`从预约生成账单成功！`, {
        description: `预约日期: ${appointmentDate}，账单编号: ${bill.billNumber}`,
        duration: 4000,
      });
    },

    loadError: (error?: string) => {
      toast.error(`加载账单失败`, {
        description: error || '请检查网络连接后重试',
        duration: 5000,
      });
    },

    createError: (error?: string) => {
      toast.error(`创建账单失败`, {
        description: error || '请检查输入信息后重试',
        duration: 5000,
      });
    },

    updateError: (error?: string) => {
      toast.error(`更新账单失败`, {
        description: error || '请稍后重试',
        duration: 5000,
      });
    },

    deleteError: (error?: string) => {
      toast.error(`删除账单失败`, {
        description: error || '请稍后重试',
        duration: 5000,
      });
    },

    validationError: (message: string) => {
      toast.error(`账单验证失败`, {
        description: message,
        duration: 5000,
      });
    },
  },

  // Payment-related notifications
  payment: {
    processed: (payment: Payment) => {
      toast.success(`支付处理成功！`, {
        description: `支付金额: ${billingUtils.formatCurrency(payment.amount)}，支付编号: ${payment.paymentNumber}`,
        duration: 5000,
      });
    },

    receiptGenerated: (payment: Payment) => {
      toast.success(`收据生成成功！`, {
        description: `收据编号: ${payment.receiptNumber || '待生成'}`,
        duration: 4000,
      });
    },

    refunded: (payment: Payment, refundAmount: number) => {
      toast.success(`退款处理成功！`, {
        description: `退款金额: ${billingUtils.formatCurrency(refundAmount)}，支付编号: ${payment.paymentNumber}`,
        duration: 5000,
      });
    },

    statusUpdated: (payment: Payment, oldStatus: string, newStatus: string) => {
      const statusNames = {
        pending: '待处理',
        completed: '已完成',
        failed: '失败',
        refunded: '已退款',
      };

      toast.success(`支付状态已更新！`, {
        description: `${payment.paymentNumber}: ${statusNames[oldStatus as keyof typeof statusNames]} → ${statusNames[newStatus as keyof typeof statusNames]}`,
        duration: 4000,
      });
    },

    processError: (error?: string) => {
      toast.error(`支付处理失败`, {
        description: error || '请检查支付信息后重试',
        duration: 5000,
      });
    },

    refundError: (error?: string) => {
      toast.error(`退款处理失败`, {
        description: error || '请联系管理员处理',
        duration: 5000,
      });
    },

    validationError: (message: string) => {
      toast.error(`支付验证失败`, {
        description: message,
        duration: 5000,
      });
    },

    amountExceeded: (maxAmount: number) => {
      toast.error(`支付金额超限`, {
        description: `最大支付金额: ${billingUtils.formatCurrency(maxAmount)}`,
        duration: 5000,
      });
    },
  },

  // Receipt-related notifications
  receipt: {
    printed: (receiptNumber: string) => {
      toast.success(`收据打印成功！`, {
        description: `收据编号: ${receiptNumber}`,
        duration: 3000,
      });
    },

    downloaded: (receiptNumber: string) => {
      toast.success(`收据下载成功！`, {
        description: `收据编号: ${receiptNumber}`,
        duration: 3000,
      });
    },

    printError: () => {
      toast.error(`收据打印失败`, {
        description: '请检查打印机设置',
        duration: 4000,
      });
    },

    downloadError: () => {
      toast.error(`收据下载失败`, {
        description: '请稍后重试',
        duration: 4000,
      });
    },

    notFound: (receiptNumber: string) => {
      toast.error(`收据未找到`, {
        description: `收据编号: ${receiptNumber}`,
        duration: 4000,
      });
    },
  },

  // General system notifications
  system: {
    loading: (action: string) => {
      toast.loading(`${action}中...`, {
        duration: Infinity, // Will be dismissed manually
      });
    },

    networkError: () => {
      toast.error(`网络连接失败`, {
        description: '请检查网络连接后重试',
        duration: 5000,
      });
    },

    permissionDenied: (action: string) => {
      toast.error(`权限不足`, {
        description: `您没有权限执行: ${action}`,
        duration: 5000,
      });
    },

    dataRefreshed: () => {
      toast.success(`数据刷新成功`, {
        duration: 2000,
      });
    },

    dataRefreshError: () => {
      toast.error(`数据刷新失败`, {
        description: '请稍后重试',
        duration: 4000,
      });
    },

    operationCancelled: (operation: string) => {
      toast.info(`${operation}已取消`, {
        duration: 2000,
      });
    },

    featureNotImplemented: (feature: string) => {
      toast.info(`${feature}功能开发中...`, {
        description: '敬请期待',
        duration: 3000,
      });
    },
  },

  // Financial reporting notifications
  financial: {
    reportGenerated: (reportType: string, period: string) => {
      toast.success(`${reportType}生成成功！`, {
        description: `报表期间: ${period}`,
        duration: 4000,
      });
    },

    reportError: (reportType: string, error?: string) => {
      toast.error(`${reportType}生成失败`, {
        description: error || '请稍后重试',
        duration: 5000,
      });
    },

    dataExported: (format: string) => {
      toast.success(`数据导出成功！`, {
        description: `格式: ${format}`,
        duration: 3000,
      });
    },

    exportError: (error?: string) => {
      toast.error(`数据导出失败`, {
        description: error || '请稍后重试',
        duration: 4000,
      });
    },
  },

  // Validation and warning notifications
  validation: {
    requiredField: (fieldName: string) => {
      toast.error(`字段验证失败`, {
        description: `${fieldName}为必填项`,
        duration: 4000,
      });
    },

    invalidFormat: (fieldName: string, expectedFormat: string) => {
      toast.error(`格式验证失败`, {
        description: `${fieldName}格式应为: ${expectedFormat}`,
        duration: 4000,
      });
    },

    duplicateEntry: (itemType: string, identifier: string) => {
      toast.error(`重复条目`, {
        description: `${itemType} "${identifier}" 已存在`,
        duration: 4000,
      });
    },

    unsavedChanges: () => {
      toast.warning(`有未保存的更改`, {
        description: '请保存后再继续',
        duration: 4000,
      });
    },

    confirmAction: (action: string) => {
      toast.warning(`请确认操作`, {
        description: `即将执行: ${action}`,
        duration: 5000,
      });
    },
  },
};

// Utility function to dismiss all toasts
export const dismissAllToasts = () => {
  toast.dismiss();
};

// Utility function to show custom toast with consistent styling
export const showCustomToast = (
  type: 'success' | 'error' | 'warning' | 'info',
  title: string,
  description?: string,
  duration: number = 4000
) => {
  const toastFunction = toast[type];
  toastFunction(title, {
    description,
    duration,
  });
};
