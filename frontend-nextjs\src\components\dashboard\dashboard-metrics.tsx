'use client';

import { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardAction,
  CardFooter
} from '@/components/ui/card';
import { IconTrendingUp, IconCalendar, IconUsers, IconStethoscope } from '@tabler/icons-react';
import { getDashboardMetrics } from '@/lib/api';
import type { DashboardMetrics } from '@/types/clinic';
import { t } from '@/lib/translations';

export default function DashboardMetrics() {
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    todayAppointments: 0,
    recentPatients: 0,
    totalPatients: 0,
    activetreatments: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchMetrics() {
      try {
        setLoading(true);
        const data = await getDashboardMetrics();
        setMetrics(data);
        setError(null);
      } catch (err) {
        console.error('Failed to fetch dashboard metrics:', err);
        setError(t('dashboard.errors.failedToLoadMetrics'));
      } finally {
        setLoading(false);
      }
    }

    fetchMetrics();
  }, []);

  if (loading) {
    return (
      <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4'>
        {[...Array(4)].map((_, i) => (
          <Card key={i} className='@container/card'>
            <CardHeader>
              <CardDescription className='flex items-center gap-2'>
                <div className='h-4 w-4 bg-muted animate-pulse rounded' />
                <div className='h-4 w-24 bg-muted animate-pulse rounded' />
              </CardDescription>
              <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>
                <div className='h-8 w-16 bg-muted animate-pulse rounded' />
              </CardTitle>
            </CardHeader>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card className='col-span-full'>
          <CardHeader>
            <CardTitle className='text-destructive'>{t('dashboard.errors.loadingDashboard')}</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4'>
      {/* Today's Appointments Card */}
      <Card className='@container/card'>
        <CardHeader>
          <CardDescription className='flex items-center gap-2'>
            <IconCalendar className='size-4' />
            {t('dashboard.metrics.todayAppointments')}
          </CardDescription>
          <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>
            {metrics.todayAppointments}
          </CardTitle>
          <CardAction>
            <Badge variant='outline'>
              <IconTrendingUp />
              {t('dashboard.metrics.active')}
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className='flex-col items-start gap-1.5 text-sm'>
          <div className='line-clamp-1 flex gap-2 font-medium'>
            {t('dashboard.metrics.scheduledForToday')} <IconCalendar className='size-4' />
          </div>
          <div className='text-muted-foreground'>
            {t('dashboard.metrics.appointmentsScheduledForToday')}
          </div>
        </CardFooter>
      </Card>

      {/* Recent Patients Card */}
      <Card className='@container/card'>
        <CardHeader>
          <CardDescription className='flex items-center gap-2'>
            <IconUsers className='size-4' />
            {t('dashboard.metrics.recentPatients')}
          </CardDescription>
          <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>
            {metrics.recentPatients}
          </CardTitle>
          <CardAction>
            <Badge variant='outline'>
              <IconTrendingUp />
              {t('dashboard.metrics.last7Days')}
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className='flex-col items-start gap-1.5 text-sm'>
          <div className='line-clamp-1 flex gap-2 font-medium'>
            {t('dashboard.metrics.newPatientsThisWeek')} <IconUsers className='size-4' />
          </div>
          <div className='text-muted-foreground'>
            {t('dashboard.metrics.patientsRegisteredInLast7Days')}
          </div>
        </CardFooter>
      </Card>

      {/* Total Patients Card */}
      <Card className='@container/card'>
        <CardHeader>
          <CardDescription className='flex items-center gap-2'>
            <IconUsers className='size-4' />
            {t('dashboard.metrics.totalPatients')}
          </CardDescription>
          <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>
            {metrics.totalPatients}
          </CardTitle>
          <CardAction>
            <Badge variant='outline'>
              <IconTrendingUp />
              {t('dashboard.metrics.allTime')}
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className='flex-col items-start gap-1.5 text-sm'>
          <div className='line-clamp-1 flex gap-2 font-medium'>
            {t('dashboard.metrics.totalRegisteredPatients')} <IconUsers className='size-4' />
          </div>
          <div className='text-muted-foreground'>
            {t('dashboard.metrics.completePatientDatabase')}
          </div>
        </CardFooter>
      </Card>

      {/* Active Treatments Card */}
      <Card className='@container/card'>
        <CardHeader>
          <CardDescription className='flex items-center gap-2'>
            <IconStethoscope className='size-4' />
            {t('dashboard.metrics.activetreatments')}
          </CardDescription>
          <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>
            {metrics.activetreatments}
          </CardTitle>
          <CardAction>
            <Badge variant='outline'>
              <IconTrendingUp />
              {t('dashboard.metrics.available')}
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className='flex-col items-start gap-1.5 text-sm'>
          <div className='line-clamp-1 flex gap-2 font-medium'>
            {t('dashboard.metrics.treatmentOptionsAvailable')} <IconStethoscope className='size-4' />
          </div>
          <div className='text-muted-foreground'>
            {t('dashboard.metrics.fullServiceCatalog')}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
