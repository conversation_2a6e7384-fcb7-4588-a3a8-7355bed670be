import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../lib/payload-auth-middleware';

/**
 * GET /api/payments - Get all payments
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Extract query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const page = parseInt(url.searchParams.get('page') || '1');
    const depth = parseInt(url.searchParams.get('depth') || '2');
    const billId = url.searchParams.get('billId');
    const patientId = url.searchParams.get('patientId');
    const paymentMethod = url.searchParams.get('paymentMethod');
    const paymentStatus = url.searchParams.get('paymentStatus');

    // Build where clause for filtering
    const where: Record<string, unknown> = {};
    if (billId) {
      where.bill = { equals: billId };
    }
    if (patientId) {
      where.patient = { equals: patientId };
    }
    if (paymentMethod) {
      where.paymentMethod = { equals: paymentMethod };
    }
    if (paymentStatus) {
      where.paymentStatus = { equals: paymentStatus };
    }

    // Fetch payments from Payload CMS with relationships
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'payments',
      'find',
      {
        limit,
        page,
        depth, // Include bill, patient, and receivedBy relationships
        where: Object.keys(where).length > 0 ? where : undefined,
        sort: '-paymentDate', // Sort by newest first
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching payments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payments' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/payments - Create a new payment
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const paymentData = await request.json();

    // Add the current user as the receiver
    paymentData.receivedBy = authContext.user.id;

    // Validate required fields
    if (!paymentData.bill) {
      return NextResponse.json(
        { error: 'Bill is required' },
        { status: 400 }
      );
    }

    if (!paymentData.patient) {
      return NextResponse.json(
        { error: 'Patient is required' },
        { status: 400 }
      );
    }

    if (!paymentData.amount || paymentData.amount <= 0) {
      return NextResponse.json(
        { error: 'Valid payment amount is required' },
        { status: 400 }
      );
    }

    if (!paymentData.paymentMethod) {
      return NextResponse.json(
        { error: 'Payment method is required' },
        { status: 400 }
      );
    }

    // Create payment in Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'payments',
      'create',
      {
        data: paymentData,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error creating payment:', error);
    return NextResponse.json(
      { error: 'Failed to create payment' },
      { status: 500 }
    );
  }
}
