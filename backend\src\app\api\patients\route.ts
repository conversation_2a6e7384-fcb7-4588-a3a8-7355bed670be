import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../lib/payload-auth-middleware';

/**
 * GET /api/patients - Get all patients
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Extract query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const page = parseInt(url.searchParams.get('page') || '1');
    
    // Handle search functionality
    const searchQuery = url.searchParams.get('where[or][0][fullName][contains]');
    let whereClause = {};
    
    if (searchQuery) {
      whereClause = {
        or: [
          {
            fullName: {
              contains: searchQuery,
            },
          },
          {
            phone: {
              contains: searchQuery,
            },
          },
        ],
      };
    }

    // Fetch patients from Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'patients',
      'find',
      {
        limit,
        page,
        where: whereClause,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching patients:', error);
    return NextResponse.json(
      { error: 'Failed to fetch patients' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/patients - Create a new patient
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const patientData = await request.json();

    // Create patient in Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'patients',
      'create',
      {
        data: patientData,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error creating patient:', error);
    return NextResponse.json(
      { error: 'Failed to create patient' },
      { status: 500 }
    );
  }
}
