'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  IconFilter, 
  IconX, 
  IconSearch,
  IconCalendar,
  IconCurrencyYuan,
  IconUser,
  IconFileText
} from '@tabler/icons-react';
import { billingUtils } from '@/lib/api/billing';

export interface BillFilterOptions {
  search?: string;
  status?: string;
  billType?: string;
  patientId?: string;
  dateFrom?: string;
  dateTo?: string;
  amountMin?: number;
  amountMax?: number;
}

interface BillFiltersProps {
  filters: BillFilterOptions;
  onFiltersChange: (filters: BillFilterOptions) => void;
  patients?: Array<{ id: string; fullName: string; phone: string }>;
  className?: string;
}

const billStatuses = [
  { value: 'draft', label: '草稿' },
  { value: 'sent', label: '已发送' },
  { value: 'confirmed', label: '已确认' },
  { value: 'paid', label: '已支付' },
  { value: 'cancelled', label: '已取消' },
];

const billTypes = [
  { value: 'treatment', label: '治疗账单' },
  { value: 'consultation', label: '咨询账单' },
  { value: 'deposit', label: '押金账单' },
  { value: 'additional', label: '补充账单' },
];

export function BillFilters({ filters, onFiltersChange, patients = [], className }: BillFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<BillFilterOptions>(filters);

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== '' && value !== null
  );

  const activeFilterCount = Object.values(filters).filter(value => 
    value !== undefined && value !== '' && value !== null
  ).length;

  const handleFilterChange = (key: keyof BillFilterOptions, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
  };

  const applyFilters = () => {
    onFiltersChange(localFilters);
    setIsOpen(false);
  };

  const clearFilters = () => {
    const emptyFilters: BillFilterOptions = {};
    setLocalFilters(emptyFilters);
    onFiltersChange(emptyFilters);
    setIsOpen(false);
  };

  const clearSingleFilter = (key: keyof BillFilterOptions) => {
    const newFilters = { ...filters };
    delete newFilters[key];
    onFiltersChange(newFilters);
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Search Bar */}
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="搜索账单编号、患者姓名或描述..."
            value={filters.search || ''}
            onChange={(e) => onFiltersChange({ ...filters, search: e.target.value })}
            className="pl-10"
          />
        </div>
        
        {/* Advanced Filters Popover */}
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="relative">
              <IconFilter className="h-4 w-4 mr-2" />
              高级筛选
              {activeFilterCount > 0 && (
                <Badge 
                  variant="secondary" 
                  className="ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                >
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          
          <PopoverContent className="w-80 p-4" align="end">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">高级筛选</h4>
                <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
                  <IconX className="h-4 w-4" />
                </Button>
              </div>

              <Separator />

              {/* Status Filter */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <IconFileText className="h-4 w-4" />
                  账单状态
                </Label>
                <Select 
                  value={localFilters.status || ''} 
                  onValueChange={(value) => handleFilterChange('status', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部状态</SelectItem>
                    {billStatuses.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Bill Type Filter */}
              <div className="space-y-2">
                <Label>账单类型</Label>
                <Select 
                  value={localFilters.billType || ''} 
                  onValueChange={(value) => handleFilterChange('billType', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部类型</SelectItem>
                    {billTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Patient Filter */}
              {patients.length > 0 && (
                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <IconUser className="h-4 w-4" />
                    患者
                  </Label>
                  <Select 
                    value={localFilters.patientId || ''} 
                    onValueChange={(value) => handleFilterChange('patientId', value || undefined)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择患者" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">全部患者</SelectItem>
                      {patients.map((patient) => (
                        <SelectItem key={patient.id} value={patient.id}>
                          {patient.fullName} - {patient.phone}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Date Range Filter */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <IconCalendar className="h-4 w-4" />
                  日期范围
                </Label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs text-muted-foreground">开始日期</Label>
                    <Input
                      type="date"
                      value={localFilters.dateFrom || ''}
                      onChange={(e) => handleFilterChange('dateFrom', e.target.value || undefined)}
                      className="text-sm"
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">结束日期</Label>
                    <Input
                      type="date"
                      value={localFilters.dateTo || ''}
                      onChange={(e) => handleFilterChange('dateTo', e.target.value || undefined)}
                      className="text-sm"
                    />
                  </div>
                </div>
              </div>

              {/* Amount Range Filter */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <IconCurrencyYuan className="h-4 w-4" />
                  金额范围
                </Label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs text-muted-foreground">最小金额</Label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      placeholder="0.00"
                      value={localFilters.amountMin || ''}
                      onChange={(e) => handleFilterChange('amountMin', parseFloat(e.target.value) || undefined)}
                      className="text-sm"
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">最大金额</Label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      placeholder="无限制"
                      value={localFilters.amountMax || ''}
                      onChange={(e) => handleFilterChange('amountMax', parseFloat(e.target.value) || undefined)}
                      className="text-sm"
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button onClick={applyFilters} className="flex-1">
                  应用筛选
                </Button>
                <Button variant="outline" onClick={clearFilters}>
                  清除
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {filters.status && (
            <Badge variant="secondary" className="flex items-center gap-1">
              状态: {billStatuses.find(s => s.value === filters.status)?.label}
              <button
                onClick={() => clearSingleFilter('status')}
                className="ml-1 hover:bg-muted rounded-full p-0.5"
              >
                <IconX className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {filters.billType && (
            <Badge variant="secondary" className="flex items-center gap-1">
              类型: {billTypes.find(t => t.value === filters.billType)?.label}
              <button
                onClick={() => clearSingleFilter('billType')}
                className="ml-1 hover:bg-muted rounded-full p-0.5"
              >
                <IconX className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {filters.patientId && (
            <Badge variant="secondary" className="flex items-center gap-1">
              患者: {patients.find(p => p.id === filters.patientId)?.fullName}
              <button
                onClick={() => clearSingleFilter('patientId')}
                className="ml-1 hover:bg-muted rounded-full p-0.5"
              >
                <IconX className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {(filters.dateFrom || filters.dateTo) && (
            <Badge variant="secondary" className="flex items-center gap-1">
              日期: {filters.dateFrom || '开始'} ~ {filters.dateTo || '结束'}
              <button
                onClick={() => {
                  clearSingleFilter('dateFrom');
                  clearSingleFilter('dateTo');
                }}
                className="ml-1 hover:bg-muted rounded-full p-0.5"
              >
                <IconX className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {(filters.amountMin !== undefined || filters.amountMax !== undefined) && (
            <Badge variant="secondary" className="flex items-center gap-1">
              金额: {filters.amountMin ? billingUtils.formatCurrency(filters.amountMin) : '0'} ~ {filters.amountMax ? billingUtils.formatCurrency(filters.amountMax) : '∞'}
              <button
                onClick={() => {
                  clearSingleFilter('amountMin');
                  clearSingleFilter('amountMax');
                }}
                className="ml-1 hover:bg-muted rounded-full p-0.5"
              >
                <IconX className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters} className="h-6 px-2">
              清除全部
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
