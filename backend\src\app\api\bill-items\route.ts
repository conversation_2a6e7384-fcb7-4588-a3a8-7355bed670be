import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../lib/payload-auth-middleware';

/**
 * GET /api/bill-items - Get all bill items
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Extract query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const page = parseInt(url.searchParams.get('page') || '1');
    const depth = parseInt(url.searchParams.get('depth') || '2');
    const billId = url.searchParams.get('billId');
    const itemType = url.searchParams.get('itemType');

    // Build where clause for filtering
    const where: Record<string, unknown> = {};
    if (billId) {
      where.bill = { equals: billId };
    }
    if (itemType) {
      where.itemType = { equals: itemType };
    }

    // Fetch bill items from Payload CMS with relationships
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'bill-items',
      'find',
      {
        limit,
        page,
        depth, // Include bill relationship
        where: Object.keys(where).length > 0 ? where : undefined,
        sort: 'createdAt', // Sort by creation order
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching bill items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch bill items' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/bill-items - Create a new bill item
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const billItemData = await request.json();

    // Validate required fields
    if (!billItemData.bill) {
      return NextResponse.json(
        { error: 'Bill is required' },
        { status: 400 }
      );
    }

    if (!billItemData.itemName) {
      return NextResponse.json(
        { error: 'Item name is required' },
        { status: 400 }
      );
    }

    if (!billItemData.quantity || billItemData.quantity <= 0) {
      return NextResponse.json(
        { error: 'Valid quantity is required' },
        { status: 400 }
      );
    }

    if (billItemData.unitPrice === undefined || billItemData.unitPrice < 0) {
      return NextResponse.json(
        { error: 'Valid unit price is required' },
        { status: 400 }
      );
    }

    // Create bill item in Payload CMS
    const result = await makeAuthenticatedPayloadRequest(
      authContext,
      'bill-items',
      'create',
      {
        data: billItemData,
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error creating bill item:', error);
    return NextResponse.json(
      { error: 'Failed to create bill item' },
      { status: 500 }
    );
  }
}
