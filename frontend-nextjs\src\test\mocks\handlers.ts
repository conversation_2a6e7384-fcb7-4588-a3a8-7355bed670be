import { http, HttpResponse } from 'msw'
import { mockAppointments, mockPatients, mockTreatments, mockUsers } from './data'

export const handlers = [
  // Appointments endpoints
  http.get('/api/appointments', () => {
    return HttpResponse.json({
      docs: mockAppointments,
      totalDocs: mockAppointments.length,
      limit: 10,
      totalPages: 1,
      page: 1,
      pagingCounter: 1,
      hasPrevPage: false,
      hasNextPage: false,
    })
  }),

  http.get('/api/appointments/:id', ({ params }) => {
    const appointment = mockAppointments.find(a => a.id === params.id)
    if (!appointment) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(appointment)
  }),

  http.post('/api/appointments', async ({ request }) => {
    const body = await request.json() as Record<string, any>
    const newAppointment = {
      id: `appointment-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...body,
    }
    return HttpResponse.json(newAppointment, { status: 201 })
  }),

  http.put('/api/appointments/:id', async ({ params, request }) => {
    const body = await request.json() as Record<string, any>
    const appointment = mockAppointments.find(a => a.id === params.id)
    if (!appointment) {
      return new HttpResponse(null, { status: 404 })
    }
    const updatedAppointment = {
      ...appointment,
      ...body,
      updatedAt: new Date().toISOString(),
    }
    return HttpResponse.json(updatedAppointment)
  }),

  http.delete('/api/appointments/:id', ({ params }) => {
    const appointment = mockAppointments.find(a => a.id === params.id)
    if (!appointment) {
      return new HttpResponse(null, { status: 404 })
    }
    return new HttpResponse(null, { status: 204 })
  }),

  // Patients endpoints
  http.get('/api/patients', () => {
    return HttpResponse.json({
      docs: mockPatients,
      totalDocs: mockPatients.length,
      limit: 10,
      totalPages: 1,
      page: 1,
      pagingCounter: 1,
      hasPrevPage: false,
      hasNextPage: false,
    })
  }),

  http.get('/api/patients/:id', ({ params }) => {
    const patient = mockPatients.find(p => p.id === params.id)
    if (!patient) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(patient)
  }),

  http.post('/api/patients', async ({ request }) => {
    const body = await request.json() as Record<string, any>
    const newPatient = {
      id: `patient-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...body,
    }
    return HttpResponse.json(newPatient, { status: 201 })
  }),

  http.put('/api/patients/:id', async ({ params, request }) => {
    const body = await request.json() as Record<string, any>
    const patient = mockPatients.find(p => p.id === params.id)
    if (!patient) {
      return new HttpResponse(null, { status: 404 })
    }
    const updatedPatient = {
      ...patient,
      ...body,
      updatedAt: new Date().toISOString(),
    }
    return HttpResponse.json(updatedPatient)
  }),

  http.delete('/api/patients/:id', ({ params }) => {
    const patient = mockPatients.find(p => p.id === params.id)
    if (!patient) {
      return new HttpResponse(null, { status: 404 })
    }
    return new HttpResponse(null, { status: 204 })
  }),

  // Treatments endpoints
  http.get('/api/treatments', () => {
    return HttpResponse.json({
      docs: mockTreatments,
      totalDocs: mockTreatments.length,
      limit: 10,
      totalPages: 1,
      page: 1,
      pagingCounter: 1,
      hasPrevPage: false,
      hasNextPage: false,
    })
  }),

  http.get('/api/treatments/:id', ({ params }) => {
    const treatment = mockTreatments.find(t => t.id === params.id)
    if (!treatment) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(treatment)
  }),

  http.post('/api/treatments', async ({ request }) => {
    const body = await request.json() as Record<string, any>
    const newTreatment = {
      id: `treatment-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...body,
    }
    return HttpResponse.json(newTreatment, { status: 201 })
  }),

  http.put('/api/treatments/:id', async ({ params, request }) => {
    const body = await request.json() as Record<string, any>
    const treatment = mockTreatments.find(t => t.id === params.id)
    if (!treatment) {
      return new HttpResponse(null, { status: 404 })
    }
    const updatedTreatment = {
      ...treatment,
      ...body,
      updatedAt: new Date().toISOString(),
    }
    return HttpResponse.json(updatedTreatment)
  }),

  http.delete('/api/treatments/:id', ({ params }) => {
    const treatment = mockTreatments.find(t => t.id === params.id)
    if (!treatment) {
      return new HttpResponse(null, { status: 404 })
    }
    return new HttpResponse(null, { status: 204 })
  }),

  // Users endpoints
  http.get('/api/users', () => {
    return HttpResponse.json({
      docs: mockUsers,
      totalDocs: mockUsers.length,
      limit: 10,
      totalPages: 1,
      page: 1,
      pagingCounter: 1,
      hasPrevPage: false,
      hasNextPage: false,
    })
  }),

  // Auth sync endpoint
  http.post('/api/auth/sync', () => {
    return HttpResponse.json({ success: true })
  }),
]
