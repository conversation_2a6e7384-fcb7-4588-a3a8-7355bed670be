// 中文翻译文件
export const translations = {
  // 导航菜单
  nav: {
    dashboard: '仪表板',
    appointments: '预约管理',
    patients: '患者管理',
    treatments: '治疗项目',
    admin: '系统管理',
    account: '账户',
    profile: '个人资料',
    login: '登录',
    overview: '概览'
  },

  // 仪表板
  dashboard: {
    title: '诊所控制台 🏥',
    subtitle: '欢迎使用您的诊所管理系统',
    metrics: {
      todayAppointments: '今日预约',
      recentPatients: '近期患者',
      totalPatients: '患者总数',
      activetreatments: '可用治疗',
      scheduledForToday: '今日安排',
      appointmentsScheduledForToday: '今日安排的预约',
      newPatientsThisWeek: '本周新患者',
      patientsRegisteredInLast7Days: '过去7天注册的患者',
      totalRegisteredPatients: '注册患者总数',
      completePatientDatabase: '完整患者数据库',
      treatmentOptionsAvailable: '可用治疗选项',
      fullServiceCatalog: '完整服务目录',
      active: '活跃',
      last7Days: '过去7天',
      allTime: '全部时间',
      available: '可用'
    },
    errors: {
      loadingDashboard: '加载仪表板时出错',
      failedToLoadMetrics: '无法加载仪表板数据'
    }
  },

  // 预约管理
  appointments: {
    title: '预约管理',
    subtitle: '管理患者预约和排程',
    newAppointment: '新建预约',
    editAppointment: '编辑预约',
    appointmentDetails: '预约详情',
    appointmentsCount: '个预约',
    loadingAppointments: '加载预约中...',
    noAppointments: '暂无预约',
    filters: {
      all: '全部',
      today: '今天',
      thisWeek: '本周',
      thisMonth: '本月',
      status: '状态',
      dateRange: '日期范围'
    },
    status: {
      scheduled: '已安排',
      confirmed: '已确认',
      inProgress: '进行中',
      completed: '已完成',
      cancelled: '已取消',
      noShow: '未到场'
    },
    form: {
      patient: '患者',
      selectPatient: '选择患者',
      treatment: '治疗项目',
      selectTreatment: '选择治疗项目',
      date: '日期',
      time: '时间',
      notes: '备注',
      notesPlaceholder: '预约备注（可选）',
      status: '状态'
    }
  },

  // 患者管理
  patients: {
    title: '患者管理',
    subtitle: '管理患者信息和病历',
    newPatient: '新建患者',
    editPatient: '编辑患者',
    patientDetails: '患者详情',
    patientsCount: '位患者',
    loadingPatients: '加载患者中...',
    noPatients: '暂无患者',
    searchPlaceholder: '按姓名、电话或邮箱搜索患者',
    form: {
      fullName: '姓名',
      fullNamePlaceholder: '请输入患者姓名',
      phone: '电话',
      phonePlaceholder: '请输入电话号码',
      email: '邮箱',
      emailPlaceholder: '请输入邮箱地址（可选）',
      medicalNotes: '病历备注',
      medicalNotesPlaceholder: '请输入病历备注（可选）'
    }
  },

  // 治疗项目
  treatments: {
    title: '治疗项目',
    subtitle: '管理诊所治疗服务',
    newTreatment: '新建治疗',
    editTreatment: '编辑治疗',
    treatmentDetails: '治疗详情',
    treatmentsCount: '个治疗项目',
    loadingTreatments: '加载治疗项目中...',
    noTreatments: '暂无治疗项目',
    form: {
      name: '治疗名称',
      namePlaceholder: '请输入治疗名称',
      description: '治疗描述',
      descriptionPlaceholder: '请输入治疗描述',
      duration: '治疗时长',
      durationPlaceholder: '请输入治疗时长（分钟）',
      price: '价格',
      pricePlaceholder: '请输入价格'
    }
  },

  // 系统管理
  admin: {
    title: '系统管理',
    subtitle: '管理用户权限和系统设置',
    userManagement: '用户管理',
    roleManagement: '角色管理',
    systemSettings: '系统设置',
    users: '用户',
    roles: {
      admin: '管理员',
      doctor: '医生',
      frontDesk: '前台'
    }
  },

  // 通用文本
  common: {
    // 操作按钮
    actions: {
      save: '保存',
      cancel: '取消',
      edit: '编辑',
      delete: '删除',
      view: '查看',
      search: '搜索',
      filter: '筛选',
      reset: '重置',
      submit: '提交',
      close: '关闭',
      confirm: '确认',
      back: '返回',
      next: '下一步',
      previous: '上一步',
      add: '添加',
      remove: '移除',
      update: '更新',
      create: '创建'
    },

    // 状态
    status: {
      loading: '加载中...',
      success: '成功',
      error: '错误',
      warning: '警告',
      info: '信息',
      pending: '待处理',
      active: '活跃',
      inactive: '非活跃',
      enabled: '已启用',
      disabled: '已禁用'
    },

    // 时间相关
    time: {
      today: '今天',
      yesterday: '昨天',
      tomorrow: '明天',
      thisWeek: '本周',
      lastWeek: '上周',
      nextWeek: '下周',
      thisMonth: '本月',
      lastMonth: '上月',
      nextMonth: '下月',
      thisYear: '今年',
      lastYear: '去年',
      nextYear: '明年'
    },

    // 确认对话框
    confirmDialog: {
      title: '确认操作',
      deleteTitle: '确认删除',
      deleteMessage: '您确定要删除这个项目吗？此操作无法撤销。',
      cancelTitle: '确认取消',
      cancelMessage: '您确定要取消吗？未保存的更改将丢失。',
      saveTitle: '确认保存',
      saveMessage: '您确定要保存这些更改吗？'
    }
  },

  // 表单验证
  validation: {
    required: '此字段为必填项',
    email: '请输入有效的邮箱地址',
    phone: '请输入有效的电话号码',
    minLength: '至少需要 {min} 个字符',
    maxLength: '最多允许 {max} 个字符',
    number: '请输入有效的数字',
    positive: '请输入正数',
    date: '请选择有效的日期',
    time: '请选择有效的时间'
  },

  // 错误消息
  errors: {
    general: '发生了未知错误，请稍后重试',
    network: '网络连接错误，请检查您的网络连接',
    unauthorized: '您没有权限执行此操作',
    notFound: '请求的资源未找到',
    serverError: '服务器错误，请稍后重试',
    validationError: '输入数据验证失败',
    loadFailed: '加载数据失败',
    saveFailed: '保存数据失败',
    deleteFailed: '删除数据失败',
    updateFailed: '更新数据失败',
    createFailed: '创建数据失败'
  },

  // 成功消息
  success: {
    saved: '保存成功',
    deleted: '删除成功',
    updated: '更新成功',
    created: '创建成功',
    sent: '发送成功',
    uploaded: '上传成功',
    downloaded: '下载成功'
  }
} as const;

// 翻译工具函数
export function t(key: string, params?: Record<string, string | number>): string {
  const keys = key.split('.');
  let value: any = translations;
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      console.warn(`Translation key not found: ${key}`);
      return key; // 返回原始key作为fallback
    }
  }
  
  if (typeof value !== 'string') {
    console.warn(`Translation value is not a string: ${key}`);
    return key;
  }
  
  // 处理参数替换
  if (params) {
    return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
      return params[paramKey]?.toString() || match;
    });
  }
  
  return value;
}

// 类型定义
export type TranslationKey = keyof typeof translations;
