import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, createMockPatient } from '@/test/utils'
import { PatientFormDialog } from '../patient-form-dialog'

describe('PatientFormDialog', () => {
  const mockOnOpenChange = vi.fn()
  const mockOnSuccess = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders create form when no patient is provided', () => {
    render(
      <PatientFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    expect(screen.getByText('New Patient')).toBeInTheDocument()
    expect(screen.getByText('Fill in the patient details to create a new record.')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /create patient/i })).toBeInTheDocument()
  })

  it('renders edit form when patient is provided', () => {
    const mockPatient = createMockPatient()
    
    render(
      <PatientFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        patient={mockPatient}
        onSuccess={mockOnSuccess}
      />
    )

    expect(screen.getByText('Edit Patient')).toBeInTheDocument()
    expect(screen.getByText('Update the patient information below.')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /update patient/i })).toBeInTheDocument()
  })

  it('pre-fills form fields when editing a patient', () => {
    const mockPatient = createMockPatient({
      fullName: 'John Doe',
      phone: '******-0123',
      email: '<EMAIL>',
      medicalNotes: 'No known allergies'
    })
    
    render(
      <PatientFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        patient={mockPatient}
        onSuccess={mockOnSuccess}
      />
    )

    // Check that form fields are pre-filled
    expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument()
    expect(screen.getByDisplayValue('******-0123')).toBeInTheDocument()
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByDisplayValue('No known allergies')).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()
    
    render(
      <PatientFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    // Try to submit without filling required fields
    const createButton = screen.getByRole('button', { name: /create patient/i })
    await user.click(createButton)

    // Should show validation errors
    await waitFor(() => {
      expect(screen.getByText(/full name must be at least 2 characters/i)).toBeInTheDocument()
      expect(screen.getByText(/phone number must be at least 10 characters/i)).toBeInTheDocument()
    })
  })

  it('validates email format', async () => {
    const user = userEvent.setup()
    
    render(
      <PatientFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    // Fill in required fields
    await user.type(screen.getByLabelText(/full name/i), 'John Doe')
    await user.type(screen.getByLabelText(/phone number/i), '******-0123')
    
    // Enter invalid email
    await user.type(screen.getByLabelText(/email address/i), 'invalid-email')
    
    const createButton = screen.getByRole('button', { name: /create patient/i })
    await user.click(createButton)

    // Should show email validation error
    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument()
    })
  })

  it('closes dialog when cancel button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <PatientFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    const cancelButton = screen.getByRole('button', { name: /cancel/i })
    await user.click(cancelButton)

    expect(mockOnOpenChange).toHaveBeenCalledWith(false)
  })

  it('displays loading state when submitting', async () => {
    const user = userEvent.setup()
    
    render(
      <PatientFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    // Fill in required fields
    await user.type(screen.getByLabelText(/full name/i), 'John Doe')
    await user.type(screen.getByLabelText(/phone number/i), '******-0123')
    
    const createButton = screen.getByRole('button', { name: /create patient/i })
    await user.click(createButton)

    // Should show loading state (this would require proper API mocking)
    // For now, just verify the form exists
    expect(screen.getByLabelText(/full name/i)).toBeInTheDocument()
  })

  it('handles optional fields correctly', async () => {
    const user = userEvent.setup()
    
    render(
      <PatientFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    // Fill in only required fields
    await user.type(screen.getByLabelText(/full name/i), 'John Doe')
    await user.type(screen.getByLabelText(/phone number/i), '******-0123')
    
    // Leave email and medical notes empty
    const createButton = screen.getByRole('button', { name: /create patient/i })
    await user.click(createButton)

    // Should not show validation errors for optional fields
    expect(screen.queryByText(/email/i)).not.toBeInTheDocument()
    expect(screen.queryByText(/medical notes/i)).not.toBeInTheDocument()
  })

  it('renders all form fields', () => {
    render(
      <PatientFormDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    )

    expect(screen.getByLabelText(/full name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/phone number/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/medical notes/i)).toBeInTheDocument()
  })
})
