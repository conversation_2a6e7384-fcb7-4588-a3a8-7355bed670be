// Enhanced error handling utilities for better user experience

export interface ApiErrorDetails {
  field?: string;
  code?: string;
  constraint?: string;
}

export interface ApiError {
  error: string;
  timestamp: string;
  details?: ApiErrorDetails;
}

/**
 * Convert API errors to user-friendly messages
 */
export function formatApiError(error: any): string {
  // Handle network errors
  if (!navigator.onLine) {
    return '无网络连接。请检查您的网络连接后重试。';
  }

  // Handle timeout errors
  if (error.name === 'AbortError' || error.message?.includes('timeout')) {
    return '请求超时。请重试。';
  }

  // Handle structured API errors
  if (error.error || error.message) {
    const errorMessage = error.error || error.message;
    
    // Handle specific database constraint errors
    if (errorMessage.includes('duplicate key') || errorMessage.includes('unique constraint')) {
      if (errorMessage.includes('phone')) {
        return '此电话号码已被注册。请使用不同的电话号码。';
      }
      if (errorMessage.includes('email')) {
        return '此邮箱地址已被注册。请使用不同的邮箱地址。';
      }
      if (errorMessage.includes('name')) {
        return '此名称已被使用。请选择不同的名称。';
      }
      return '此信息已被使用。请检查您的输入后重试。';
    }

    // Handle validation errors
    if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
      return '请检查您的输入，确保所有必填字段都正确填写。';
    }

    // Handle permission errors
    if (errorMessage.includes('permission') || errorMessage.includes('unauthorized') || errorMessage.includes('forbidden')) {
      return '您没有权限执行此操作。请联系您的管理员。';
    }

    // Handle not found errors
    if (errorMessage.includes('not found') || errorMessage.includes('does not exist')) {
      return '找不到请求的项目。它可能已被删除或移动。';
    }

    // Handle appointment-specific errors
    if (errorMessage.includes('appointment')) {
      if (errorMessage.includes('conflict') || errorMessage.includes('overlap')) {
        return '此预约时间与其他预约冲突。请选择不同的时间。';
      }
      if (errorMessage.includes('past')) {
        return '无法安排过去的预约。请选择未来的日期和时间。';
      }
    }

    // Handle patient-specific errors
    if (errorMessage.includes('patient')) {
      if (errorMessage.includes('appointments')) {
        return '无法删除有预约记录的患者。请先取消或完成所有预约。';
      }
    }

    // Handle treatment-specific errors
    if (errorMessage.includes('treatment')) {
      if (errorMessage.includes('appointments')) {
        return '无法删除有预约记录的治疗项目。请先取消或完成所有预约。';
      }
    }

    // Return the original error message if it's already user-friendly
    if (errorMessage.length < 100 && !errorMessage.includes('Error:') && !errorMessage.includes('failed')) {
      return errorMessage;
    }
  }

  // Handle HTTP status codes
  if (error.status) {
    switch (error.status) {
      case 400:
        return '请求无效。请检查您的输入后重试。';
      case 401:
        return '您没有权限执行此操作。请登录后重试。';
      case 403:
        return '您没有权限执行此操作。';
      case 404:
        return '找不到请求的项目。';
      case 409:
        return '此操作与现有数据冲突。请检查您的输入。';
      case 422:
        return '提供的数据无效。请检查您的输入后重试。';
      case 429:
        return '请求过于频繁。请稍等片刻后重试。';
      case 500:
        return '服务器错误。请稍后重试。';
      case 502:
      case 503:
      case 504:
        return '服务暂时不可用。请稍后重试。';
      default:
        return '发生了意外错误。请重试。';
    }
  }

  // Fallback for unknown errors
  return '发生了意外错误。请重试，如果问题持续存在，请联系技术支持。';
}

/**
 * Format success messages for different operations
 */
export function formatSuccessMessage(operation: string, entity: string): string {
  const entityMap: Record<string, string> = {
    'patient': '患者',
    'appointment': '预约',
    'treatment': '治疗项目',
    'user': '用户'
  };

  const entityName = entityMap[entity.toLowerCase()] || entity;

  switch (operation) {
    case 'create':
      return `${entityName}创建成功！`;
    case 'update':
      return `${entityName}更新成功！`;
    case 'delete':
      return `${entityName}删除成功！`;
    case 'complete':
      return `${entityName}已标记为完成！`;
    case 'cancel':
      return `${entityName}取消成功！`;
    case 'sync':
      return `${entityName}同步成功！`;
    default:
      return `${entityName}${operation}完成！`;
  }
}

/**
 * Format loading messages for different operations
 */
export function formatLoadingMessage(operation: string, entity: string): string {
  const entityMap: Record<string, string> = {
    'patient': '患者',
    'appointment': '预约',
    'treatment': '治疗项目',
    'user': '用户'
  };

  const entityName = entityMap[entity.toLowerCase()] || entity;

  switch (operation) {
    case 'create':
      return `正在创建${entityName}...`;
    case 'update':
      return `正在更新${entityName}...`;
    case 'delete':
      return `正在删除${entityName}...`;
    case 'load':
      return `正在加载${entityName}...`;
    case 'save':
      return `正在保存${entityName}...`;
    case 'sync':
      return `正在同步${entityName}...`;
    default:
      return `正在处理${entityName}...`;
  }
}

/**
 * Validation error formatter for form fields
 */
export function formatValidationError(field: string, error: string): string {
  const fieldMap: Record<string, string> = {
    'fullName': '姓名',
    'phone': '电话',
    'email': '邮箱',
    'medicalNotes': '病历备注',
    'appointmentDate': '预约日期',
    'treatment': '治疗项目',
    'patient': '患者',
    'price': '价格',
    'duration': '时长',
    'name': '名称',
    'description': '描述'
  };

  const fieldName = fieldMap[field] || field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1');

  // Common validation patterns
  if (error.includes('required')) {
    return `${fieldName}为必填项。`;
  }
  if (error.includes('email')) {
    return `请输入有效的邮箱地址。`;
  }
  if (error.includes('phone')) {
    return `请输入有效的电话号码。`;
  }
  if (error.includes('min')) {
    const match = error.match(/(\d+)/);
    const min = match ? match[1] : '';
    return `${fieldName}至少需要${min}个字符。`;
  }
  if (error.includes('max')) {
    const match = error.match(/(\d+)/);
    const max = match ? match[1] : '';
    return `${fieldName}不能超过${max}个字符。`;
  }

  return error;
}

/**
 * Check if error is a network error
 */
export function isNetworkError(error: any): boolean {
  return !navigator.onLine || 
         error.name === 'NetworkError' || 
         error.message?.includes('fetch') ||
         error.message?.includes('network');
}

/**
 * Check if error is a permission error
 */
export function isPermissionError(error: any): boolean {
  const message = (error.error || error.message || '').toLowerCase();
  return message.includes('permission') || 
         message.includes('unauthorized') || 
         message.includes('forbidden') ||
         error.status === 401 ||
         error.status === 403;
}

/**
 * Check if error is a validation error
 */
export function isValidationError(error: any): boolean {
  const message = (error.error || error.message || '').toLowerCase();
  return message.includes('validation') || 
         message.includes('invalid') ||
         message.includes('required') ||
         error.status === 400 ||
         error.status === 422;
}

/**
 * Get appropriate toast duration based on error severity
 */
export function getToastDuration(error: any): number {
  if (isNetworkError(error)) {
    return 8000; // Longer for network issues
  }
  if (isPermissionError(error)) {
    return 6000; // Medium for permission issues
  }
  if (isValidationError(error)) {
    return 4000; // Shorter for validation issues
  }
  return 5000; // Default duration
}
