'use client';

// Role-based access control context and hooks
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { AuthenticatedUser } from './auth-middleware';
import { UserRole, hasPermission, hasRole, getRolePermissions, RolePermissions } from './role-utils';

interface RoleContextType {
  user: AuthenticatedUser | null;
  loading: boolean;
  error: string | null;
  permissions: RolePermissions | null;
  hasPermission: (permission: keyof RolePermissions) => boolean;
  hasRole: (roles: UserRole | UserRole[]) => boolean;
  refreshUser: () => Promise<void>;
}

const RoleContext = createContext<RoleContextType | undefined>(undefined);

interface RoleProviderProps {
  children: React.ReactNode;
}

/**
 * Role Provider Component
 * Fetches user role information and provides role-based utilities
 */
export function RoleProvider({ children }: RoleProviderProps) {
  const { user: clerkUser, isLoaded: clerkLoaded } = useUser();
  const [user, setUser] = useState<AuthenticatedUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserRole = async () => {
    if (!clerkUser) {
      setUser(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Call the auth sync endpoint to get user role from backend
      const response = await fetch('/api/auth/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to sync user data');
      }

      const data = await response.json();

      // Use the synced user data from backend
      const authenticatedUser: AuthenticatedUser = {
        clerkId: clerkUser.id,
        email: clerkUser.emailAddresses[0]?.emailAddress || '',
        firstName: clerkUser.firstName || undefined,
        lastName: clerkUser.lastName || undefined,
        role: data.user?.role || 'front-desk', // Use role from backend or default
        payloadUserId: data.user?.payloadUserId, // Set from backend response
      };

      setUser(authenticatedUser);
    } catch (err) {
      console.error('Error fetching user role:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch user role');
      
      // Set user with default role on error
      if (clerkUser) {
        setUser({
          clerkId: clerkUser.id,
          email: clerkUser.emailAddresses[0]?.emailAddress || '',
          firstName: clerkUser.firstName || undefined,
          lastName: clerkUser.lastName || undefined,
          role: 'front-desk', // Default fallback role
          payloadUserId: undefined,
        });
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (clerkLoaded) {
      fetchUserRole();
    }
  }, [clerkUser?.id, clerkLoaded]); // Use clerkUser.id instead of the whole object

  const permissions = user?.role ? getRolePermissions(user.role) : null;

  const contextValue: RoleContextType = {
    user,
    loading,
    error,
    permissions,
    hasPermission: (permission: keyof RolePermissions) => hasPermission(user, permission),
    hasRole: (roles: UserRole | UserRole[]) => hasRole(user, roles),
    refreshUser: fetchUserRole,
  };

  return (
    <RoleContext.Provider value={contextValue}>
      {children}
    </RoleContext.Provider>
  );
}

/**
 * Hook to access role context
 */
export function useRole(): RoleContextType {
  const context = useContext(RoleContext);
  if (context === undefined) {
    throw new Error('useRole must be used within a RoleProvider');
  }
  return context;
}

/**
 * Hook for permission checking
 */
export function usePermission(permission: keyof RolePermissions): boolean {
  const { hasPermission } = useRole();
  return hasPermission(permission);
}

/**
 * Hook for role checking
 */
export function useHasRole(roles: UserRole | UserRole[]): boolean {
  const { hasRole } = useRole();
  return hasRole(roles);
}

/**
 * Component wrapper for conditional rendering based on permissions
 */
interface PermissionGateProps {
  permission: keyof RolePermissions;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function PermissionGate({ permission, children, fallback = null }: PermissionGateProps) {
  const hasRequiredPermission = usePermission(permission);
  
  return hasRequiredPermission ? <>{children}</> : <>{fallback}</>;
}

/**
 * Component wrapper for conditional rendering based on roles
 */
interface RoleGateProps {
  roles: UserRole | UserRole[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function RoleGate({ roles, children, fallback = null }: RoleGateProps) {
  const hasRequiredRole = useHasRole(roles);
  
  return hasRequiredRole ? <>{children}</> : <>{fallback}</>;
}

/**
 * Higher-order component for role-based access control
 */
export function withRoleAccess<P extends object>(
  Component: React.ComponentType<P>,
  requiredRoles: UserRole | UserRole[],
  fallbackComponent?: React.ComponentType<P>
) {
  return function RoleProtectedComponent(props: P) {
    const hasRequiredRole = useHasRole(requiredRoles);
    
    if (!hasRequiredRole) {
      if (fallbackComponent) {
        const FallbackComponent = fallbackComponent;
        return <FallbackComponent {...props} />;
      }
      return (
        <div className="p-4 text-center text-muted-foreground">
          <p>You don't have permission to access this feature.</p>
        </div>
      );
    }
    
    return <Component {...props} />;
  };
}

/**
 * Loading component for role context
 */
export function RoleLoading() {
  return (
    <div className="flex items-center justify-center p-4">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      <span className="ml-2 text-muted-foreground">Loading user permissions...</span>
    </div>
  );
}
