import type { CollectionConfig, Access } from 'payload'

export const Payments: CollectionConfig = {
  slug: 'payments',
  admin: {
    useAsTitle: 'paymentNumber',
    defaultColumns: ['paymentNumber', 'bill', 'patient', 'amount', 'paymentMethod', 'paymentStatus'],
    listSearchableFields: ['paymentNumber', 'bill.billNumber', 'patient.fullName'],
  },
  access: {
    // Read: Same as Bills - Admin and Front-desk see all, Doctors see only related payments
    read: (({ req: { user } }) => {
      if (!user) return false;
      if (user.role === 'admin' || user.role === 'front-desk') {
        return true;
      }
      if (user.role === 'doctor') {
        return {
          or: [
            {
              'bill.appointment.practitioner': {
                equals: user.id,
              },
            },
            {
              receivedBy: {
                equals: user.id,
              },
            },
          ],
        };
      }
      return false;
    }) as Access,

    // Create: Admin and Front-desk can create payments
    create: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin' || user.role === 'front-desk';
    },

    // Update: Admin and Front-desk can update payments
    update: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin' || user.role === 'front-desk';
    },

    // Delete: Only Admin can delete payments
    delete: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin';
    },
  },
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Auto-generate payment number if not provided
        if (!data.paymentNumber) {
          const now = new Date();
          const year = now.getFullYear();
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const day = String(now.getDate()).padStart(2, '0');
          const timestamp = now.getTime().toString().slice(-6);
          data.paymentNumber = `PAY-${year}${month}${day}-${timestamp}`;
        }

        // Auto-generate receipt number if not provided and payment is completed
        if (!data.receiptNumber && data.paymentStatus === 'completed') {
          const now = new Date();
          const year = now.getFullYear();
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const day = String(now.getDate()).padStart(2, '0');
          const timestamp = now.getTime().toString().slice(-6);
          data.receiptNumber = `REC-${year}${month}${day}-${timestamp}`;
        }

        return data;
      },
    ],
  },
  fields: [
    {
      name: 'paymentNumber',
      type: 'text',
      required: true,
      unique: true,
      label: '支付编号',
      admin: {
        description: '系统自动生成，格式：PAY-YYYYMMDD-XXXXXX',
        readOnly: true,
      },
    },
    
    // 关联信息
    {
      name: 'bill',
      type: 'relationship',
      relationTo: 'bills',
      required: true,
      hasMany: false,
      label: '关联账单',
    },
    {
      name: 'patient',
      type: 'relationship',
      relationTo: 'patients',
      required: true,
      hasMany: false,
      label: '患者',
    },
    
    // 支付信息
    {
      name: 'amount',
      type: 'number',
      required: true,
      label: '支付金额',
      min: 0.01,
      admin: {
        description: '本次支付的金额',
      },
    },
    {
      name: 'paymentMethod',
      type: 'select',
      required: true,
      options: [
        {
          label: '现金',
          value: 'cash',
        },
        {
          label: '银行卡',
          value: 'card',
        },
        {
          label: '微信支付',
          value: 'wechat',
        },
        {
          label: '支付宝',
          value: 'alipay',
        },
        {
          label: '银行转账',
          value: 'transfer',
        },
        {
          label: '分期付款',
          value: 'installment',
        },
      ],
      label: '支付方式',
    },
    {
      name: 'paymentStatus',
      type: 'select',
      required: true,
      options: [
        {
          label: '待处理',
          value: 'pending',
        },
        {
          label: '已完成',
          value: 'completed',
        },
        {
          label: '失败',
          value: 'failed',
        },
        {
          label: '已退款',
          value: 'refunded',
        },
      ],
      defaultValue: 'pending',
      label: '支付状态',
    },
    
    // 支付详情
    {
      name: 'transactionId',
      type: 'text',
      label: '交易ID',
      admin: {
        description: '第三方支付平台的交易ID（如适用）',
      },
    },
    {
      name: 'paymentDate',
      type: 'date',
      required: true,
      label: '支付日期',
      defaultValue: () => new Date().toISOString(),
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'receivedBy',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      hasMany: false,
      label: '收款人员',
      admin: {
        description: '处理此次支付的工作人员',
      },
    },
    
    // 备注信息
    {
      name: 'notes',
      type: 'textarea',
      label: '支付备注',
      admin: {
        description: '支付相关的备注信息',
      },
    },
    {
      name: 'receiptNumber',
      type: 'text',
      label: '收据编号',
      admin: {
        description: '收据编号（系统自动生成）',
        readOnly: true,
      },
    },
  ],
}
