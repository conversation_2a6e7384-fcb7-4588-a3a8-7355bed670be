import type { CollectionConfig, Access } from 'payload'

export const BillItems: CollectionConfig = {
  slug: 'bill-items',
  admin: {
    useAsTitle: 'itemName',
    defaultColumns: ['bill', 'itemName', 'quantity', 'unitPrice', 'totalPrice'],
    listSearchableFields: ['itemName', 'description'],
  },
  access: {
    // Read: Same as Bills - Admin and Front-desk see all, Doctors see only related items
    read: (({ req: { user } }) => {
      if (!user) return false;
      if (user.role === 'admin' || user.role === 'front-desk') {
        return true;
      }
      if (user.role === 'doctor') {
        return {
          or: [
            {
              'bill.appointment.practitioner': {
                equals: user.id,
              },
            },
            {
              'bill.createdBy': {
                equals: user.id,
              },
            },
          ],
        };
      }
      return false;
    }) as Access,

    // Create: Admin and Front-desk can create bill items
    create: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin' || user.role === 'front-desk';
    },

    // Update: Admin and Front-desk can update bill items
    update: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin' || user.role === 'front-desk';
    },

    // Delete: Admin and Front-desk can delete bill items
    delete: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin' || user.role === 'front-desk';
    },
  },
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Calculate total price based on quantity, unit price, and discount
        const subtotal = (data.quantity || 0) * (data.unitPrice || 0);
        const discountAmount = subtotal * ((data.discountRate || 0) / 100);
        data.totalPrice = subtotal - discountAmount;
        
        return data;
      },
    ],
  },
  fields: [
    // 关联账单
    {
      name: 'bill',
      type: 'relationship',
      relationTo: 'bills',
      required: true,
      hasMany: false,
      label: '所属账单',
    },
    
    // 项目信息
    {
      name: 'itemType',
      type: 'select',
      required: true,
      options: [
        {
          label: '治疗项目',
          value: 'treatment',
        },
        {
          label: '咨询服务',
          value: 'consultation',
        },
        {
          label: '材料费用',
          value: 'material',
        },
        {
          label: '其他服务',
          value: 'service',
        },
      ],
      defaultValue: 'treatment',
      label: '项目类型',
    },
    {
      name: 'itemId',
      type: 'text',
      label: '项目ID',
      admin: {
        description: '关联的治疗或服务的ID（可选）',
      },
    },
    {
      name: 'itemName',
      type: 'text',
      required: true,
      label: '项目名称',
      admin: {
        description: '账单项目的名称',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: '项目描述',
      admin: {
        description: '项目的详细描述',
      },
    },
    
    // 数量和价格
    {
      name: 'quantity',
      type: 'number',
      required: true,
      defaultValue: 1,
      label: '数量',
      min: 0.01,
      admin: {
        description: '项目数量',
      },
    },
    {
      name: 'unitPrice',
      type: 'number',
      required: true,
      label: '单价',
      min: 0,
      admin: {
        description: '项目单价',
      },
    },
    {
      name: 'discountRate',
      type: 'number',
      defaultValue: 0,
      label: '折扣率 (%)',
      min: 0,
      max: 100,
      admin: {
        description: '折扣率，0-100之间的数值',
      },
    },
    {
      name: 'totalPrice',
      type: 'number',
      label: '小计金额',
      admin: {
        description: '该项目的总金额（自动计算）',
        readOnly: true,
      },
    },
  ],
}
